{"name": "Prohelpify", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.804.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@microsoft/clarity": "^1.0.0", "@mui/material": "^7.0.2", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tabler/icons-react": "^3.30.0", "@tanstack/react-query": "^5.80.7", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "contentful-management": "^11.52.2", "cookies-next": "^5.1.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.4.10", "i18next": "^24.2.3", "i18next-resources-to-backend": "^1.2.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.474.0", "mongodb": "^6.15.0", "mongoose": "^8.12.1", "motion": "^12.18.1", "next": "15.0.3", "next-i18n-router": "^5.5.1", "next-intl": "^3.26.0", "next-sitemap": "^4.2.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-i18next": "^15.4.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "validator": "^13.15.0", "zod": "^3.24.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.9", "@types/node": "22.12.0", "@types/react": "19.0.8", "@types/validator": "^13.12.3", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "5.7.3"}}