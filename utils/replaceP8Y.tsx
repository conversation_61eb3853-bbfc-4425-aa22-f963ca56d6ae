import React from 'react';
import AnimatedP8Y from '@/components/AnimatedP8Y';

/**
 * Utility function to replace "P8Y" text with AnimatedP8Y component
 * @param text - The text that may contain "P8Y"
 * @param className - Optional className for the AnimatedP8Y component
 * @param showTooltip - Whether to show the tooltip (default: true)
 * @returns JSX element with P8Y replaced by AnimatedP8Y component
 */
export const replaceP8YInText = (
  text: string, 
  className?: string, 
  showTooltip: boolean = true
): React.ReactNode => {
  // Split text by P8Y (case insensitive)
  const parts = text.split(/(P8Y|p8y)/gi);
  
  return parts.map((part, index) => {
    if (part.toLowerCase() === 'p8y') {
      return (
        <AnimatedP8Y 
          key={index} 
          className={className} 
          showTooltip={showTooltip} 
        />
      );
    }
    return part;
  });
};

/**
 * React component that automatically replaces P8Y in children text
 */
interface P8YTextProps {
  children: string;
  className?: string;
  showTooltip?: boolean;
}

export const P8YText: React.FC<P8YTextProps> = ({ 
  children, 
  className, 
  showTooltip = true 
}) => {
  return <>{replaceP8YInText(children, className, showTooltip)}</>;
};
