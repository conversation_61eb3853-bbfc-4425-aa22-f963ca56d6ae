"use client";

import Image from "next/image";
import React, { useState, useEffect } from "react";
import { LinkedIn_Icon } from "@/utils/svg";
import Button from "./Button";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { MultiStepLoader } from "@/components/ui/multi-step-loader";

interface Mentor {
  _id: string;
  name_en: string;
  name_ar: string;
  bio_en: string;
  bio_ar: string;
  profile_image: {
    url: string;
    path: string;
  };
  linkedin_link?: string;
  calendly_link?: string;
  topics?: Array<{ title: string }>;
  skills?: Array<{ title: string }>;
  current_company?: { name: string };
  level?: string;
  jobTitle?: string;
  english_session?: boolean;
}

interface TeamProps {
  isRTL: boolean;
}

const DynamicTeam: React.FC<TeamProps> = ({ isRTL }) => {
  const [teamItems, setTeamItems] = useState<Mentor[]>([]);
  const [bioExpanded, setBioExpanded] = useState<Record<number, boolean>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [loadingMeet, setLoadingMeet] = useState(false);
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null);

  const t = useTranslations("team");
  const p = useTranslations("practices");
  const loadingSteps = p.raw("items") as { text: string }[];

  const fetchMentors = async () => {
    try {
      const response = await fetch("https://prohelpify.com/api/mentors?page=1&limit=4");
      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || "Failed to fetch mentors");
      }

      setTeamItems(data.data.data || []);
    } catch (error) {
      setError(t("fetchError"));
      setTeamItems([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMentors();
  }, []);

  const toggleBio = (index: number) => {
    setBioExpanded((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  const handleMeetClick = (link: string) => {
    setRedirectUrl(link);
    setLoadingMeet(true); // يفتح الـ Loader
    // window.open(link, "_blank"); // يفتح الرابط في تبويب جديد دون التأثير على الـ Loader
  };

  return (
    <main id="team" className="relative container max-lg:px-4 max-sm:px-3 section-padding">
      <h2 className="text-4xl text-white text-center font-bold mb-20">
        <span className="text-main_color">{t("title").split(" ")[0]}</span>{" "}
        {t("title").split(" ").slice(1).join(" ")}
      </h2>

      <MultiStepLoader
        loadingStates={loadingSteps}
        loading={loadingMeet}
        duration={1000}
        loop={false}
        bookingLink={redirectUrl || undefined}
        onClose={() => setLoadingMeet(false)}
      />
      {loading ? (
        <div className="grid grid-cols-2 max-sm:grid-cols-1 gap-8">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-[#1D2B3B] rounded-xl p-5 animate-pulse">
              <div className="flex max-md:flex-col justify-around w-full">
                <div className="p-3 max-lg:w-full w-1/3">
                  <div className="w-full h-[200px] bg-gray-700 rounded-md" />
                </div>
                <div className="max-lg:w-full w-2/3">
                  <div className="h-6 bg-gray-700 rounded w-3/4 mb-4" />
                  <div className="h-4 bg-gray-700 rounded w-1/2 mb-4" />
                  <div className="h-4 bg-gray-700 rounded w-1/3" />
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : error ? (
        <div className="text-center py-16">
          <p className="text-red-500">{error}</p>
        </div>
      ) : teamItems.length === 0 ? (
        <div className="text-center py-16">
          <p className="text-gray-500">{t("noMentors")}</p>
        </div>
      ) : (
        <div className="grid grid-cols-2 max-sm:grid-cols-1 gap-8">
          {teamItems.slice(0, 4).map((mentor, index) => (
            <div key={mentor._id} className="bg-[#1D2B3B] rounded-xl p-5 relative">
              <div className="flex max-md:flex-col justify-around w-full">
                <div className="p-3 max-lg:w-full w-1/3">
                  <Image
                    src={mentor.profile_image?.url || "/default-image.jpg"}
                    width={550}
                    height={550}
                    loading="lazy"
                    alt={`${isRTL ? mentor.name_ar : mentor.name_en}`}
                    className="w-full h-auto mx-auto rounded-md object-cover"
                  />
                </div>


                <div className="max-lg:w-full w-2/3">
                  <div className="flex justify-between">
                    <h3 className="text-xl font-bold p-2 w-full">
                      {isRTL ? mentor.name_ar : mentor.name_en}
                    </h3>
                    <div className="bg-[#EDF8FF] rounded-xl px-2 py-1 my-auto">
                      <p className="text-[0.75rem] text-[#2754B8] font-medium w-max">
                        {mentor.current_company?.name || "N/A"}
                      </p>
                    </div>
                  </div>
                  <div className="flex">
                    <p className="text-sm p-2">{mentor.jobTitle || mentor.level || "Mentor"}</p>
                    <div className="p-2">
                      {mentor.linkedin_link && (
                        <a href={mentor.linkedin_link} target="_blank" rel="noopener noreferrer">
                          {LinkedIn_Icon}
                        </a>
                      )}
                    </div>
                  </div>
                  <ul className="flex flex-wrap py-3">
                    {mentor.topics?.map((topic) => (
                      <li
                        key={`topic-${topic.title}`}
                        className="px-2 p-1 m-1 rounded-full text-xs font-medium bg-[#3C4E63]"
                      >
                        {topic.title}
                      </li>
                    ))}
                    {mentor.skills?.map((skill) => (
                      <li
                        key={`skill-${skill.title}`}
                        className="px-2 p-1 m-1 rounded-full text-xs font-medium bg-[#3C4E63]"
                      >
                        {skill.title}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="px-3 py-2 mb-10 text-[1rem] font-normal">
                <p>
                  {(isRTL ? mentor.bio_ar : mentor.bio_en) &&
                    (bioExpanded[index] ||
                      (isRTL ? mentor.bio_ar : mentor.bio_en).length < 175)
                    ? isRTL
                      ? mentor.bio_ar
                      : mentor.bio_en
                    : `${(isRTL ? mentor.bio_ar : mentor.bio_en)?.slice(0, 175)}...`}
                  {(isRTL ? mentor.bio_ar : mentor.bio_en)?.length > 175 && (
                    <span
                      onClick={() => toggleBio(index)}
                      className="ml-2 text-sm text-white font-semibold cursor-pointer hover:text-main_color hover:underline"
                    >
                      {bioExpanded[index] ? t("showLess") : t("showMore")}
                    </span>
                  )}
                </p>
              </div>

              <div className="absolute bottom-4">
                {mentor.calendly_link && (
                  <div onClick={() => handleMeetClick(mentor.calendly_link!)}>
                    <Button title={t("meetExperts")} />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="text-center mt-16">
        <Link href="/mentors">
          <button className="team-toggle">{t("showMore")}</button>
        </Link>
      </div>
    </main>
  );
};

export default DynamicTeam;
