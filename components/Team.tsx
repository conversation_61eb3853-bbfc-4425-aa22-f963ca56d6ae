"use client";
import Image from "next/image";
import React, { useState, useEffect } from "react";
import { LinkedIn_Icon } from "@/utils/svg";
import Button from "./Button";
import { useTranslations } from "next-intl";
import MeetinDialog from "./MeetinDialog";
import { getCookie } from "cookies-next";
import { useCookie } from "./context/CookieContext";

const Team = ({ isRTL }: any) => {
  const [showAll, setShowAll] = useState(false);
  const [animate, setAnimate] = useState(false);
  const [shuffledTeamItems, setShuffledTeamItems] = useState([]);
  const [open, setOpen] = useState(false);
  const [bioExpanded, setBioExpanded] = useState([]); // Track expanded bio state for each mentor
  const t = useTranslations("team");
  const { dialogOpened, updateDialogOpened } = useCookie();

  useEffect(() => {
    const dialogOpened = getCookie("dialogOpened") === "true";
    setOpen(dialogOpened);
  }, [open]);
  let teamItems;
  try {
    teamItems = t.raw("items");
  } catch (error) {
    console.error("Translation error:", error);
    teamItems = {};
  }

  const teamItemsArray = teamItems ? Object.values(teamItems) : [];

  const shuffleArray:any = (array:any) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  useEffect(() => {
    setShuffledTeamItems(shuffleArray(teamItemsArray));
  }, []);

  const toggleItems = () => {
    const currentScrollPosition = window.scrollY;

    if (!showAll) {
      setAnimate(true);
    }
    setShowAll((prev) => !prev);

    setTimeout(() => {
      window.scrollTo({ top: currentScrollPosition, behavior: "instant" });
    }, 0);
  };

  useEffect(() => {
    if (showAll) {
      window.scrollTo({ behavior: "smooth" });
      setAnimate(false);
    }
  }, [showAll]);

  const toggleBio = (index:any) => {
    setBioExpanded((prev:any) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  return (
    <main id="team" className="relative container max-lg:px-4 max-sm:px-3 section-padding">
      <h2
        className="text-4xl text-white text-center font-bold mb-20"
        aria-label="Meet Our Team of Experts"
      >
        <span className="text-main_color">{t("title").split(" ")[0]}</span>{" "}
        {t("title").split(" ").slice(1).join(" ")}
      </h2>

      <div className="absolute bottom-0 -left-20 -z-10 overflow-hidden h-[50rem] w-[50rem] bg-custom-radial-purple" />

      <div className="grid grid-cols-2 max-sm:grid-cols-1 gap-8">
        {(showAll ? shuffledTeamItems : shuffledTeamItems.slice(0, 4)).map(
          (mentor:any, index:number) => (
            <div
              key={mentor.name}
              className={`bg-[#1D2B3B] rounded-xl p-5 transition-opacity duration-500 ease-in-out relative ${animate ? "opacity-0" : "opacity-100"
                }`}
              style={{ transitionDelay: `${(index % 4) * 100}ms` }}
            >
              <div className="flex max-md:flex-col justify-around w-full">
                <div className="p-3 max-lg:w-full w-1/3">
                  <Image
                    src={mentor.image || "/default-image.jpg"}
                    width={550}
                    height={550}
                    loading="lazy"
                    priority={false}
                    alt={`${mentor.name || "Mentor"} - ${mentor.position || "Senior Programmer"
                      }`}
                    className=" max-sm:w-full  rounded-md object-cover"
                  />
                </div>

                <div className="max-lg:w-full w-2/3">
                  <div className="flex justify-between">
                    <h3 className="text-xl font-bold p-2 w-full">
                      {mentor.name || " "}
                    </h3>
                    <div className="bg-[#EDF8FF] 30px rounded-xl px-2 sm:w-fit max-w-25 text-wrap  py-1 my-auto">
                      <p className="text-[0.75rem] text-[#2754B8] text-wrap font-medium  w-[65px] max-w-fit sm:max-w-fit sm:w-[65px] lg:w-max">
                        {mentor.company || "N/A"}
                      </p>
                    </div>
                  </div>
                  <div className="flex">
                    <p className="text-sm p-2">{mentor.position || " "}</p>
                    <div className="text-[1rem] font-semibold p-2 my-auto cursor-pointer hover:text-blue-700">
                      {mentor.linkedin && (
                        <a
                          href={mentor.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          aria-label={`${mentor.name}'s LinkedIn Profile`}
                        >
                          {LinkedIn_Icon}
                        </a>
                      )}
                    </div>
                  </div>
                  <div className="py-3 flex w-full">
                    <ul className="flex flex-wrap">
                      {Object.keys(mentor.topics || {}).map(
                        (topic) =>
                          mentor.topics[topic] && (
                            <li
                              key={topic}
                              className="px-2 p-1 m-1 w-fit rounded-full text-xs font-medium bg-[#3C4E63]"
                            >
                              {topic}
                            </li>
                          )
                      )}
                    </ul>
                  </div>
                </div>
              </div>
              <div className="px-3 py-2 mb-10 flex items-end justify-between text-[1rem] font-normal">
                <p className="flex-1">
                  {mentor.bio && bioExpanded[index] || mentor.bio.length < 175
                    ? mentor.bio
                    : `${mentor.bio?.slice(0, 175)}...`}
                  {mentor.bio.length > 175 && <span onClick={() => toggleBio(index)}
                    className="ml-2 text-sm text-white p-1 font-semibold cursor-pointer hover:text-main_color hover:underline duration-300"> {bioExpanded[index] ? t("showLess") : t("showMore")}</span>}
                </p>

              </div>

              <div className="absolute bottom-4">
                {mentor.booking &&
                  (dialogOpened ? (
                    <a
                      href={mentor.booking}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <Button title={t("meetExperts")} />
                    </a>
                  ) : (
                    <MeetinDialog
                      title={t("meetExperts")}
                      booking={mentor.booking}
                      name={mentor.name}
                      isRtl={isRTL}
                      onConfirm={() => updateDialogOpened(true)}
                    />
                  ))}
              </div>
            </div>
          )
        )}
      </div>

      <div className="text-center mt-16">
        <button
          onClick={toggleItems}
          className="team-toggle"
          aria-label={
            showAll ? "Show less team members" : "Show more team members"
          }
        >
          {showAll ? t("showLess") : t("showMore")}
        </button>
      </div>

      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebPage",
          name: "Meet Our Team of Experts - Prohelpify",
          description:
            "Learn coding, debugging, and best practices from our team of senior programmers at Prohelpify.",
          url: "https://www.example.com/team",
          image: "https://www.example.com/og-image.jpg",
        })}
      </script>
    </main>
  );
};

export default Team;
