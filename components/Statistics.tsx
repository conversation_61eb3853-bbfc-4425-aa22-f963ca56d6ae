"use client";
import React, { useEffect, useState, useRef } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { useTranslations } from "next-intl";

interface StatItem {
  value: number;
  suffix?: string;
  title: string;
  description: string;
}

interface StatisticsProps {
  isRTL: boolean;
}

const Statistics: React.FC<StatisticsProps> = ({ isRTL }) => {
  const sectionRef = useRef<HTMLDivElement | null>(null);
  const [inView, setInView] = useState<boolean>(false);
  const t = useTranslations("statistics");

  const stats: StatItem[] = Array.isArray(t.raw("stats")) ? t.raw("stats") : [];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (sectionRef.current) observer.observe(sectionRef.current);

    return () => observer.disconnect();
  }, []);

  return (
    <main id="statistics" className="relative container px-3 max-lg:px-4 max-sm:px-3 section-padding">
      <h2 
      className="text-4xl text-white text-center font-bold mb-20"
      // className="font-bold text-2xl md:text-4xl text-center mb-20"
      >{t("title")}</h2>
      <div ref={sectionRef} className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full">
        <div className="flex flex-col gap-6">
          <StatCard stat={stats[0]} isRTL={isRTL} inView={inView} />
          <StatCard stat={stats[1]} isRTL={isRTL} inView={inView} />
        </div>
        <div className="flex">
          <StatCard stat={stats[2]} inView={inView} className="w-full p-6" />
        </div>
        <div className="flex flex-col gap-6">
          <StatCard stat={stats[3]} isRTL={isRTL} inView={inView} />
          <StatCard stat={stats[4]} isRTL={isRTL} inView={inView} />
        </div>
      </div>
    </main>
  );
};

interface StatCardProps {
  stat: StatItem;
  inView: boolean;
  isRTL?: boolean;
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({ stat, inView, isRTL = false, className = "" }) => {
  const [count, setCount] = useState<number>(0);
  const [hasAnimated, setHasAnimated] = useState<boolean>(false);

  useEffect(() => {
    if (inView && !hasAnimated && stat?.value) {
      let currentCount = 0;
      const targetCount = stat.value;
      const duration = 2000;
      const frameDuration = 1000 / 60; // 60fps
      const totalFrames = Math.round(duration / frameDuration);
      const increment = targetCount / totalFrames;

      const animate = () => {
        currentCount += increment;
        if (currentCount >= targetCount) {
          setCount(targetCount);
          setHasAnimated(true);
        } else {
          setCount(Math.floor(currentCount));
          requestAnimationFrame(animate);
        }
      };

      requestAnimationFrame(animate);
    }
  }, [inView, stat?.value, hasAnimated]);

  if (!stat) return null;

  return (
    <Card
      className={`p-6 m-3 sm:m-0 h-full flex flex-col justify-center shadow-md hover:shadow-lg transition duration-300 ${className}`}
    >
      <CardHeader>
        <CardTitle className="text-5xl text-main_color font-extrabold">
          {count}
          {stat.suffix}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <h3 className={`text-2xl font-semibold text-white ${isRTL ? "text-right" : ""}`}>
          {stat.title}
        </h3>
        <p className="text-gray-300 mt-2">{stat.description}</p>
      </CardContent>
    </Card>
  );
};

export default Statistics;
