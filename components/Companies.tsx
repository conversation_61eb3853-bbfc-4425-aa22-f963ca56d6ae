import Image from "next/image";
import React from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { useTranslations } from "use-intl";
import { companies } from "@/utils/constant";
import Link from "next/link";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface CompaniesProps {
  isRTL: boolean;
}

const Companies :React.FC<CompaniesProps> = ({ isRTL }) => {
  const t = useTranslations("companies");

  return (
    <main id="companies" className="relative container px-3 max-lg:px-4 max-sm:px-3 section-padding">
      <h2
        className="text-4xl text-white text-center font-bold mb-20"
        // className="font-bold text-2xl md:text-4xl text-center mb-20"
        aria-label="About Prohelpify"
      >
        {t("title")}
      </h2>
      <div className="flex flex-wrap justify-center w-full">
        <Carousel
          dir={isRTL ? "rtl" : "ltr"} 
          plugins={[
            Autoplay({
              delay: 2000,
              stopOnInteraction: false,
            }),
          ]}
          opts={{
            loop: true, 
            align: "start", 
            direction: isRTL ? "rtl" : "ltr", 
          }}
        >
          <CarouselContent>
            {companies.map((company, index) => (
              <CarouselItem
                key={`${company.name}-${index}`}
                className="sm:basis-1/5 lg:basis-[10%] basis-1/3"
              >
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger className="!p-0 !bg-transparent ">
                      {/* <Link href={company.website} target="_blanc" rel="noopener noreferrer"> */}
                        <Image
                          className="object-contain rounded-lg sm:grayscale hover:grayscale-0 duration-300"
                          src={company.logo}
                          width={100}
                          height={100}
                          alt={`${company.name} company logo`}
                          loading="lazy"
                          priority={false}
                        />
                      {/* </Link> */}
                    </TooltipTrigger>
                    <TooltipContent className={'bg-[#1D2B3B]'}>
                      <p className="text-[16px] p-1">{company.name}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>
      </div>
    </main>
  );
};

export default Companies;
