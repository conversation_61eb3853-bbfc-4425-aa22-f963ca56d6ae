"use client";
import React, { useState, useRef, useEffect } from "react";
import { useTranslations } from "next-intl";
import { Button } from "./ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { useIsSafari } from "@/hooks/useIsSafari";
import Image from "next/image";
import AnimatedButtonPreview from "./startNowButtton";
import Clarity from "@microsoft/clarity";
import MentorWizard from "./SmartForm";
import P8YText from "./P8YText";

const Landing = ({ isRTL }) => {
  const t = useTranslations("landing");
  const wizard = useTranslations("wizard");
  const [isVideoLoading, setIsVideoLoading] = useState(false);
  const videoRef = useRef(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      Clarity.upgrade('heatmap-data-collection');
    }
  }, []);

  const isSafari = useIsSafari();
  const styles = {
    width: "35px",
    height: "35px",
    borderRadius: "100%",
    borderWidth: "2px",
    borderStyle: "solid",
    borderColor: "rgb(54, 215, 183) rgb(54, 215, 183) transparent",
    borderImage: "initial",
    display: "inline-block",
  };

  useEffect(() => {
    const video = videoRef.current;

    const handleVideoLoading = () => {
      setIsVideoLoading(true);

      setTimeout(() => {
        setIsVideoLoading(false);
      }, 3000);
    };

    const handleVideoLoaded = () => {
      setIsVideoLoading(false);
    };

    const handleVideoPlay = () => {
      setIsVideoLoading(false);
    };

    const handleVideoEnded = () => {
      setIsVideoLoading(true);

      setTimeout(() => {
        setIsVideoLoading(false);
      }, 3000);
    };

    video.addEventListener("waiting", handleVideoLoading);
    video.addEventListener("canplay", handleVideoLoaded);
    video.addEventListener("playing", handleVideoPlay);
    video.addEventListener("ended", handleVideoEnded);

    return () => {
      video.removeEventListener("waiting", handleVideoLoading);
      video.removeEventListener("canplay", handleVideoLoaded);
      video.removeEventListener("playing", handleVideoPlay);
      video.removeEventListener("ended", handleVideoEnded);
    };
  }, []);

  return (
    <main id="home" className="section-padding relative">


      <div className="landing-container relative z-30">
        <div className="lg:w-1/2 max-md:text-center flex flex-col justify-evenly relative">
          <h1 className="font-bold text-[3rem] max-md:text-[1.5rem] md:leading-[3.6rem]  ">
            {t("title")
              .split(" ")
              .map((word, index) => (
                <span
                  key={index}
                  className={
                    ["free", "tech", "career!", "مجاني", "مسيرتك", "التقنية!"].includes(word.toLowerCase())
                      ? "text-main_color"
                      : ""
                  }
                >
                  {word}{" "}
                </span>
              ))}
          </h1>
          <p className="landing-desc md:leading-8">
            <P8YText text={t("description")} showTooltip={false} />
          </p>
          <div className="flex gap-3 flex-col sm:flex-row items-center justify-center md:justify-start">
            <div className="relative">
              <Dialog>
                <DialogTrigger asChild>
                  <div>
                    <AnimatedButtonPreview />
                  </div>
                </DialogTrigger>
                <DialogContent className="">
                  <DialogHeader>
                    <div className="flex justify-between items-center">
                      <DialogTitle className='text-center flex-grow'>
                        <h2 className="md:text-4xl text-xl font-bold md:mb-6 mb-0 mt-5 text-center text-white">
                          {wizard("title_1")} <span className="text-main_color">{wizard("title_2")}</span> {wizard("title_3")}
                        </h2>
                      </DialogTitle>

                    </div>
                  </DialogHeader>
                    <MentorWizard />
                </DialogContent>
              </Dialog>
            </div>

            {/* <a
              href="https://docs.google.com/forms/d/e/1FAIpQLSeHvYQw5cgMy_kNF3z9U7NkurHx_ww6xOgfik2gjmClxwfLuQ/viewform?fbzx=1530270317560478948"
              target="_blanc"
              className="relative z-40"
              rel="noopener noreferrer"
              aria-label="Meet our team of experts"
            >
              <Button
                variant="outline"
                className="!border-main_color text-md font-medium w-44 h-auto py-2 px-4 hover:bg-main_color hover:text-white"
              >
                {t("feedback_button")}
              </Button>
            </a> */}
          </div>
        </div>

        <div className="relative">
          <video
            ref={videoRef}
            autoPlay
            loop
            muted
            playsInline
            className={`mx-auto w-full h-full ${isSafari ? "hidden" : ""}`}
            loading="lazy"
            aria-label="Prohelpify - Learn Coding and Debugging"
          >
            <source
              src="/assists/Hero video -Prohelpify.mp4"
              type="video/mp4; codecs=avc1.42E01E,mp4a.40.2"
            />
            Your browser does not support the video tag.
          </video>

          {isSafari && (
            <Image
              src="/assists/hero-image.jpg"
              alt="Prohelpify - Learn Coding and Debugging"
              width={800}
              height={600}
              className="mx-auto w-full h-full object-cover"
              priority
            />
          )}

          {isVideoLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 z-10">
              <span className="animate-spin" style={styles}></span>
            </div>
          )}
        </div>

        <div
          className={`absolute order-3 z-0 bottom-0 ${isRTL ? "left-0" : "right-0"
            } overflow-hidden h-[34rem] w-[34rem] bg-custom-radial-purple`}
        />
      </div>
    </main>
  );
};

export default Landing;

