import React from 'react';
import AnimatedP8Y from './AnimatedP8Y';

interface P8YTextProps {
  text: string;
  className?: string;
  showTooltip?: boolean;
}

/**
 * Component that replaces {P8Y} placeholders in text with AnimatedP8Y components
 */
const P8YText: React.FC<P8YTextProps> = ({ 
  text, 
  className = "", 
  showTooltip = true 
}) => {
  // Split text by {P8Y} placeholder
  const parts = text.split(/(\{P8Y\})/gi);
  
  return (
    <>
      {parts.map((part, index) => {
        if (part.toLowerCase() === '{p8y}') {
          return (
            <AnimatedP8Y 
              key={index} 
              className={className} 
              showTooltip={showTooltip} 
            />
          );
        }
        return part;
      })}
    </>
  );
};

export default P8YText;
