"use client";
import React, { useState, useEffect } from 'react';
import { X, Lightbulb } from 'lucide-react';

const P8YFloatingFact = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Check if user has already dismissed this
    const dismissed = localStorage.getItem('p8y-fact-dismissed');
    if (dismissed) {
      setIsDismissed(true);
      return;
    }

    // Show the fact after 3 seconds
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    localStorage.setItem('p8y-fact-dismissed', 'true');
  };

  const handleShow = () => {
    setIsVisible(true);
  };

  if (isDismissed && !isVisible) {
    return (
      <button
        onClick={handleShow}
        className="fixed bottom-6 right-6 z-50 bg-main_color hover:bg-main_color/90 text-white p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
        title="Show P8Y fun fact"
      >
        <Lightbulb className="w-5 h-5" />
      </button>
    );
  }

  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50 max-w-sm">
      <div className="bg-gradient-to-r from-gray-800 to-gray-900 border border-main_color/30 rounded-lg p-4 shadow-xl animate-in slide-in-from-bottom-5 duration-500">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-main_color rounded-full flex items-center justify-center">
              <Lightbulb className="w-4 h-4 text-white" />
            </div>
          </div>
          
          <div className="flex-1">
            <h3 className="text-white font-semibold text-sm mb-2">
              💡 Fun Fact!
            </h3>
            <p className="text-gray-300 text-sm leading-relaxed">
              <span className="font-mono text-main_color">P8Y</span> is short for{' '}
              <span className="font-mono text-white">ProhelpifY</span> - there are exactly{' '}
              <span className="text-main_color font-semibold">8 letters</span> between P and Y!
            </p>
            <div className="mt-3 text-xs text-gray-400 font-mono">
              P-<span className="text-main_color">r-o-h-e-l-p-i-f</span>-Y
            </div>
          </div>
          
          <button
            onClick={handleDismiss}
            className="flex-shrink-0 text-gray-400 hover:text-white transition-colors duration-200"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default P8YFloatingFact;
