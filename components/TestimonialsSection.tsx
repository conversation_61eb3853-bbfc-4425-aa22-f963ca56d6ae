"use client";

import { useLocale, useTranslations } from "next-intl";
import { useState, useEffect } from "react";

interface Testimonial {
  name: string;
  gender: "boy" | "girl";
  mentor: string;
  skill: string;
  content: string;
}

// Custom Arrow Components
const LeftArrow = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
  </svg>
);

const RightArrow = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
  </svg>
);

const QuoteIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="currentColor" viewBox="0 0 24 24">
    <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
  </svg>
);

// Function to get unique gradient colors for each testimonial
const getAvatarGradient = (name: string, index: number) => {
  const gradients = [
    'from-blue-500 to-purple-600',
    'from-green-500 to-teal-600',
    'from-pink-500 to-rose-600',
    'from-orange-500 to-red-600',
    'from-indigo-500 to-blue-600',
    'from-purple-500 to-pink-600',
    'from-teal-500 to-green-600',
    'from-yellow-500 to-orange-600',
    'from-cyan-500 to-blue-600',
    'from-emerald-500 to-teal-600'
  ];

  // Use name hash + index to get consistent but unique gradient
  const nameHash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const gradientIndex = (nameHash + index) % gradients.length;

  return gradients[gradientIndex];
};

export function TestimonialsSection() {
  const locale = useLocale();
  const isRTL = locale === "ar";
  const t = useTranslations("testimonials");
  const mentor: string = t("mentor");
  const topic: string = t("topic");
  const testimonials: Testimonial[] = t.raw("array");

  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const nextTestimonial = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
    setTimeout(() => setIsAnimating(false), 300);
  };

  const prevTestimonial = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
    setTimeout(() => setIsAnimating(false), 300);
  };

  const goToTestimonial = (index: number) => {
    if (isAnimating || index === currentIndex) return;
    setIsAnimating(true);
    setCurrentIndex(index);
    setTimeout(() => setIsAnimating(false), 300);
  };

  // Auto-play functionality
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isAnimating) {
        setIsAnimating(true);
        setCurrentIndex((prev) => (prev + 1) % testimonials.length);
        setTimeout(() => setIsAnimating(false), 300);
      }
    }, 5000); // Change every 5 seconds

    return () => clearInterval(interval);
  }, [currentIndex, isAnimating, testimonials.length]);

  if (!testimonials || testimonials.length === 0) {
    return null;
  }

  const currentTestimonial = testimonials[currentIndex];
  const avatarGradient = getAvatarGradient(currentTestimonial.name, currentIndex);
  const firstLetter = currentTestimonial.name.charAt(0).toUpperCase();

  return (
    <section className="relative container max-lg:px-4 max-sm:px-3 section-padding">
      {/* Background decorative element - matching website style */}
      <div className="absolute bottom-0 -right-20 -z-10 overflow-hidden h-[50rem] w-[50rem] bg-custom-radial-purple" />

      <div className="relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t("title")}
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            {t("subtitle")}
          </p>
        </div>

        {/* Main testimonial display */}
        <div className="max-w-6xl mx-auto">
          <div className="bg-[#1D2B3B] rounded-xl p-8 md:p-12 shadow-2xl">
            <div className="grid md:grid-cols-2 gap-8 md:gap-12 items-center">

              {/* Avatar and info */}
              <div className={`text-center ${isRTL ? 'md:text-right' : 'md:text-left'}`}>
                <div className="relative inline-block mb-6">
                  <div className={`w-32 h-32 md:w-40 md:h-40 mx-auto ${isRTL ? 'md:mr-0' : 'md:ml-0'} rounded-full border-4 border-white/20 shadow-xl transition-all duration-500 bg-gradient-to-br ${avatarGradient} flex items-center justify-center`}>
                    <span className="text-4xl md:text-5xl font-bold text-white">
                      {firstLetter}
                    </span>
                  </div>
                  <div className={`absolute -top-2 w-8 h-8 bg-main_color rounded-full flex items-center justify-center ${isRTL ? '-left-2' : '-right-2'}`}>
                    <QuoteIcon className="w-4 h-4 text-white" />
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-2xl md:text-3xl font-bold text-white">
                    {currentTestimonial.name}
                  </h3>
                  <p className="text-main_color font-semibold">
                    <span className="text-gray-300">{mentor}:</span> {currentTestimonial.mentor}
                  </p>
                  <p className="text-gray-400">
                    <span className="text-gray-300">{topic}:</span> {currentTestimonial.skill}
                  </p>
                </div>
              </div>

              {/* Testimonial content */}
              <div className="space-y-6">
                <div className={`transition-all duration-300 ${isAnimating ? 'opacity-0 transform translate-y-4' : 'opacity-100 transform translate-y-0'}`}>
                  <blockquote className="text-lg md:text-xl text-gray-200 leading-relaxed italic">
                    &ldquo;{currentTestimonial.content}&rdquo;
                  </blockquote>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation controls */}
        <div className={`flex items-center justify-center mt-12 gap-6 ${isRTL ? 'flex-row-reverse' : 'flex-row'}`}>
          {/* Previous button */}
          <button
            onClick={prevTestimonial}
            disabled={isAnimating}
            className="p-3 rounded-full bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Previous testimonial"
          >
            <LeftArrow className="w-6 h-6 text-white" />
          </button>

          {/* Next button */}
          <button
            onClick={nextTestimonial}
            disabled={isAnimating}
            className="p-3 rounded-full bg-gray-800/50 hover:bg-gray-700/50 border border-gray-600/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            aria-label="Next testimonial"
          >
            <RightArrow className="w-6 h-6 text-white" />
          </button>
        </div>

        {/* Counter */}
        <div className="text-center mt-6">
          <span className="text-gray-400 text-sm">
            {currentIndex + 1} / {testimonials.length}
          </span>
        </div>
      </div>
    </section>
  );
}