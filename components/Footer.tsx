"use client";
import Image from "next/image";
import React from "react";
import { FooterSocialMedia } from "../utils/constant";
import { useLocale, useTranslations } from "next-intl";
import Link from "next/link";

interface SocialMediaItem {
  id: number;
  name: string;
  link: string;
  icon: string;
}

const Footer: React.FC<{ isRTL: boolean }> = ({ isRTL }) => {
  const footer = useTranslations("footer");
  const locale = useLocale();

  const renderLink = (href: string, label: string) => (
    <Link
      href={href}
      target="_blank"
      rel="noreferrer"
      aria-label={label}
      className="hover:text-main_color_hover transition-colors"
    >
      {label}
    </Link>
  );

  return (
    <footer className="relative overflow-hidden mt-[6rem]" lang={locale}>
      <div className="footer-container">
        <div className="flex flex-col col-span-2 justify-center gap-5 mx-auto w-full">
          <Image
            src="/assists/icons/P8Y.svg"
            alt="Prohelpify Logo"
            width={100}
            height={100}
            loading="lazy"
            className="mx-auto"
            priority={false}
          />
          <p className="font-thin text-4xl text-text_color mx-auto">
            <strong>Pro</strong>helpify
          </p>
        </div>
        <div className="col-span-4"/>
        
        <div className="flex flex-col col-span-2 gap-10 max-md:gap-5 z-40">
          <p className="font-semibold text-xl text-center">{footer("title")}</p>
          <div className="flex justify-center gap-7 cursor-pointer">
            {FooterSocialMedia.map((item) => (
              <a
                key={item.id}
                href={item.link}
                target="_blank"
                rel="noreferrer"
                aria-label={`Follow us on ${item.name}`}
              >
                <Image
                  src={item.icon}
                  alt={`${item.name} icon`}
                  width={30}
                  height={30}
                  loading="lazy"
                  priority={false}
                  className={item.className}
                />
              </a>
            ))}
          </div>
        </div>

        <div
          className={`absolute -bottom-24 ${
            isRTL ? "right-0" : "left-0"
          } z-0 overflow-hidden h-[30rem] w-[30rem] bg-custom-radial-green`}
        />
      </div>

      <div className="flex gap-x-10 justify-between max-md:flex-col px-12 py-2 max-md:text-center bg-[#080809]">
        <p className="text-sm max-md:my-2">
          Vibrant Path @{new Date().getFullYear()} | All rights reserved
        </p>
        <div className="flex text-sm z-50 underline max-md:mx-auto text-main_color">
          {renderLink(`/${locale}/privacy-policy`, "Privacy Policy")}
          {"  |  "}
          {renderLink(`/${locale}/terms-and-conditions`, "Terms of Service (ToS)")}
        </div>
      </div>
    </footer>
  );
};

export default Footer;