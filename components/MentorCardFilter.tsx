import Image from "next/image";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/lib/type";
import { useLocale, useTranslations } from "next-intl";
import { LinkedIn_Icon } from "@/utils/svg";
import Button from "./Button";
import { MultiStepLoader } from "@/components/ui/multi-step-loader";

export const MentorCard = ({ mentor }: { mentor: <PERSON><PERSON> }) => {
    const locale = useLocale();
    const isRTL = locale === 'ar';
    const [bioExpanded, setBioExpanded] = useState(false);
    const [loadingMeet, setLoadingMeet] = useState(false);
    const [redirectUrl, setRedirectUrl] = useState<string | null>(null);

    const t = useTranslations("team");
    const p = useTranslations("practices");
    const loadingSteps = p.raw("items") as { text: string }[];

    const toggleBio = () => {
        setBioExpanded(!bioExpanded);
    };

    const handleMeetClick = (link: string) => {
        setRedirectUrl(link);
        setLoadingMeet(true);
    };

    return (
        <>
            <MultiStepLoader
                loadingStates={loadingSteps}
                loading={loadingMeet}
                duration={1000}
                loop={false}
                bookingLink={redirectUrl || undefined}
                onClose={() => setLoadingMeet(false)}
            />

            <div className="bg-[#1D2B3B] rounded-xl p-5 relative">
                <div className="flex max-md:flex-col justify-around w-full">
                    <div className="p-3 max-lg:w-full w-1/3">
                        <Image
                            src={mentor.profile_image?.url || "/default-image.jpg"}
                            width={550}
                            height={550}
                            loading="lazy"
                            alt={`${isRTL ? mentor.name_ar : mentor.name_en}`}
                            className="w-full h-auto mx-auto rounded-md object-cover"
                        />
                    </div>

                    <div className="max-lg:w-full w-2/3">
                        <div className="flex justify-between">
                            <h3 className="text-xl font-bold p-2 w-full">
                                {isRTL ? mentor.name_ar : mentor.name_en}
                            </h3>
                            <div className="bg-[#EDF8FF] rounded-xl px-2 py-1 my-auto">
                                <p className="text-[0.75rem] text-[#2754B8] font-medium w-max">
                                    {mentor.current_company?.name || "N/A"}
                                </p>
                            </div>
                        </div>
                        <div className="flex">
                            <p className="text-sm p-2">{mentor.jobTitle || mentor.level || "Mentor"}</p>
                            <div className="p-2">
                                {mentor.linkedin_link && (
                                    <a href={mentor.linkedin_link} target="_blank" rel="noopener noreferrer">
                                        {LinkedIn_Icon}
                                    </a>
                                )}
                            </div>
                        </div>
                        <ul className="flex flex-wrap py-3">
                            {mentor.topics?.map((topic) => (
                                <li
                                    key={`topic-${topic.title}`}
                                    className="px-2 p-1 m-1 rounded-full text-xs font-medium bg-[#3C4E63]"
                                >
                                    {topic.title}
                                </li>
                            ))}
                            {mentor.skills?.map((skill) => (
                                <li
                                    key={`skill-${skill.title}`}
                                    className="px-2 p-1 m-1 rounded-full text-xs font-medium bg-[#3C4E63]"
                                >
                                    {skill.title}
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>

                <div className="px-3 py-2 mb-10 text-[1rem] font-normal">
                    <p>
                        {(isRTL ? mentor.bio_ar : mentor.bio_en) &&
                            (bioExpanded ||
                                (isRTL ? mentor.bio_ar : mentor.bio_en).length < 175)
                            ? isRTL
                                ? mentor.bio_ar
                                : mentor.bio_en
                            : `${(isRTL ? mentor.bio_ar : mentor.bio_en)?.slice(0, 175)}...`}
                        {(isRTL ? mentor.bio_ar : mentor.bio_en)?.length > 175 && (
                            <span
                                onClick={toggleBio}
                                className="ml-2 text-sm text-white font-semibold cursor-pointer hover:text-main_color hover:underline"
                            >
                                {bioExpanded ? t("showLess") : t("showMore")}
                            </span>
                        )}
                    </p>
                </div>

                <div className="absolute bottom-4">
                    {mentor.calendly_link && (
                        <div onClick={() => handleMeetClick(mentor.calendly_link!)}>
                            <Button title={t("meetExperts")} />
                        </div>
                    )}
                </div>
            </div>
        </>
    );
};

export  const MentorCardSkeleton = () => (
    <div className="bg-[#1D2B3B] rounded-xl p-5 animate-pulse">
      <div className="flex max-md:flex-col justify-around w-full">
        <div className="p-3 max-lg:w-full w-1/3">
          <div className="w-full h-[200px] bg-gray-700 rounded-md" />
        </div>
        <div className="max-lg:w-full w-2/3">
          <div className="h-6 bg-gray-700 rounded w-3/4 mb-4" />
          <div className="h-4 bg-gray-700 rounded w-1/2 mb-4" />
          <div className="h-4 bg-gray-700 rounded w-1/3" />
        </div>
      </div>
    </div>
  );