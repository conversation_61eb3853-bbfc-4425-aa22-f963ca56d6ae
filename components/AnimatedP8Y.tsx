"use client";
import React, { useState, useRef, useEffect } from 'react';

interface AnimatedP8YProps {
  className?: string;
  showTooltip?: boolean;
}

const AnimatedP8Y: React.FC<AnimatedP8YProps> = ({
  className = "",
  showTooltip = true
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [expandedWidth, setExpandedWidth] = useState(0);
  const expandedRef = useRef<HTMLDivElement>(null);

  // The 8 letters between P and Y in "ProhelpifY"
  const middleLetters = ['r', 'o', 'h', 'e', 'l', 'p', 'i', 'f'];

  // Calculate the width needed for the expanded text
  useEffect(() => {
    if (expandedRef.current) {
      setExpandedWidth(expandedRef.current.offsetWidth);
    }
  }, []);

  return (
    <div
      className={`relative inline-block cursor-help transition-all duration-300 ${className}`}
      style={{
        width: isHovered ? `${expandedWidth}px` : 'auto',
        minWidth: isHovered ? `${expandedWidth}px` : 'auto'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Hidden element to measure expanded width */}
      <div
        ref={expandedRef}
        className="absolute top-0 left-0 opacity-0 pointer-events-none whitespace-nowrap"
        aria-hidden="true"
      >
        <span className="font-semibold">P</span>
        {middleLetters.map((letter, index) => (
          <span key={index} className="font-medium">{letter}</span>
        ))}
        <span className="font-semibold">Y</span>
      </div>

      {/* Default state - just P8Y */}
      <div className={`transition-opacity duration-300 ${isHovered ? 'opacity-0' : 'opacity-100'}`}>
        <span className="font-semibold">P8Y</span>
      </div>

      {/* Animated state - showing all letters */}
      <div className={`absolute top-0 left-0 transition-opacity duration-300 ${isHovered ? 'opacity-100' : 'opacity-0'}`}>
        <div className="flex items-center whitespace-nowrap">
          {/* P */}
          <span className="font-semibold text-main_color">P</span>

          {/* The 8 middle letters with staggered animation */}
          {middleLetters.map((letter, index) => (
            <span
              key={index}
              className={`font-medium text-gray-300 transition-all duration-200 ${
                isHovered
                  ? 'opacity-100 transform translate-y-0'
                  : 'opacity-0 transform translate-y-2'
              }`}
              style={{
                transitionDelay: isHovered ? `${index * 50}ms` : '0ms'
              }}
            >
              {letter}
            </span>
          ))}

          {/* Y */}
          <span className="font-semibold text-main_color">Y</span>
        </div>
      </div>

      {/* Tooltip explaining the concept */}
      {showTooltip && (
        <div className={`absolute -bottom-8 left-1/2 transform -translate-x-1/2 transition-all duration-300 z-50 ${
          isHovered ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'
        }`}>
          <div className="bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap shadow-lg">
            8 letters between P and Y!
          </div>
        </div>
      )}
    </div>
  );
};

export default AnimatedP8Y;
