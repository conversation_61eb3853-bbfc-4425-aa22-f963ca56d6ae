import React, { <PERSON> } from "react";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "./ui/button";
import Link from "next/link";
import { ArrowRight, ArrowLeft } from "lucide-react";

interface AboutUsSectionProps {
  isRTL: boolean;
}

const AboutUsSection: FC<AboutUsSectionProps> = ({ isRTL }) => {
  const t = useTranslations("about");

  return (
    <section id="about-us-section" className="container mx-auto px-4 py-16 max-w-6xl">
      <div className="text-center mb-12">
        <h2 className="font-bold text-4xl max-md:text-3xl text-white mb-6">
          <span className="text-main_color">{t("about.title")}</span>
          {isRTL ? "" : " P8Y"}
        </h2>
        <p className="font-normal text-text_color text-lg leading-8 max-w-4xl mx-auto">
          {t("about.briefDescription")}
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8 mb-12">
        {[1, 2, 3].map((index) => (
          <div
            key={index}
            className="flex flex-col items-center text-center p-6 rounded-lg bg-gray-800/30 border border-gray-700/50"
          >
            <div className="w-12 h-12 rounded-full bg-main_color/20 flex items-center justify-center mb-4">
              <div className="w-6 h-6 rounded-full bg-main_color" />
            </div>
            <h3 className="font-bold text-white text-lg mb-3">
              {t(`Why.${index}.title`)}
            </h3>
            <p className="font-normal text-text_color text-sm leading-6">
              {t(`Why.${index}.content`)}
            </p>
          </div>
        ))}
      </div>

      {/* Learn More Button */}
      <div className="flex justify-center">
        <Link href="/about-us">
          <Button
            className="bg-main_color hover:bg-main_color/90 text-white px-8 py-3 rounded-lg font-medium flex items-center gap-2 group transition-all duration-200"
          >
            <span>{t("learnMore", { defaultValue: "Learn More About Us" })}</span>
            {isRTL ? (
              <ArrowLeft className="w-4 h-4 transition-transform group-hover:-translate-x-1" />
            ) : (
              <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
            )}
          </Button>
        </Link>
      </div>
    </section>
  );
};

export default AboutUsSection;
