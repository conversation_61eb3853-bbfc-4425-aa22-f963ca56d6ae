"use client";

import Image from "next/image";
import React, { useState, useEffect, useContext, ChangeEvent } from "react";
import { NavItems } from "@/utils/constant";
import { useTranslations } from "next-intl";
import { useRouter, usePathname } from "next/navigation";
import { LoadingContext } from "@/app/[locale]/context/LoadingContext";
import "./style.css";
import Link from "next/link";

type LoadingContextType = {
  setIsLoading: (loading: boolean) => void;
};

const Navbar: React.FC = () => {
  const t = useTranslations("header");
  const router = useRouter();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState("");

  const loadingContext = useContext(LoadingContext) as LoadingContextType | null;
  const setIsLoading = loadingContext?.setIsLoading ?? (() => {});
  const currentLocale = pathname.split("/")[1] || "en";

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleNavigation = async (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
    e.preventDefault();
    const isHomePage = pathname === `/${currentLocale}` || pathname === "/";
    
    if (!isHomePage) {
      // If we're not on the home page, navigate to home with hash
      router.push(`/${currentLocale}/#${targetId}`);
      
      // Add a more robust check for element and scroll after page load
      const checkElementAndScroll = () => {
        const element = document.getElementById(targetId);
        if (element) {
          setTimeout(() => {
            element.scrollIntoView({ behavior: 'smooth' });
          }, 100);
        } else {
          // If element not found yet, try again
          setTimeout(checkElementAndScroll, 100);
        }
      };

      // Start checking for element after a short delay to allow for page load
      setTimeout(checkElementAndScroll, 500);
    } else {
      // If we're already on the home page, just scroll
      const element = document.getElementById(targetId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    }
    toggleMenu();
  };

  const handleLanguageChange = async (e: ChangeEvent<HTMLSelectElement>) => {
    const newLocale = e.target.value;
    setIsLoading(true);

    let newPath;
    if (pathname === "/") {
      newPath = `/${newLocale}`;
    } else if (pathname.startsWith(`/${currentLocale}/`)) {
      newPath = pathname.replace(`/${currentLocale}`, `/${newLocale}`);
    } else if (pathname.startsWith(`/${currentLocale}`)) {
      newPath = pathname.replace(`/${currentLocale}`, `/${newLocale}`);
    } else {
      newPath = `/${newLocale}${pathname}`;
    }

    await new Promise((resolve) => setTimeout(resolve, 1000));
    router.push(newPath);
    setIsLoading(false);
  };

  useEffect(() => {
    const handleScroll = () => {
      let currentSection = "";
      NavItems.forEach((item) => {
        const section = document.getElementById(item.toLowerCase());
        if (section) {
          const { top } = section.getBoundingClientRect();
          if (top <= 50 && top >= -section.offsetHeight / 2) {
            currentSection = item.toLowerCase();
          }
        }
      });
      setActiveSection(currentSection);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <nav className="top-0 left-0 w-full z-[100] fixed">
      <div className="Nav flex justify-between items-center px-14 md:py-4 py-3 w-full max-h-fit bg-[#121c29ce] relative">
        <Link href={`/${currentLocale}`} className="logo flex items-center">
          <Image
            src="/assists/icons/P8Y.svg"
            alt="Prohelpify logo"
            width={30}
            height={30}
            loading="lazy"
            priority={false}
          />
          <p className="mx-1.5 font-extralight">
            <span className="font-semibold">Pro</span>helpify
          </p>
        </Link>

        <div className="md:hidden cursor-pointer z-50" onClick={toggleMenu}>
          <div className={`hamburger-icon ${isMenuOpen ? "open" : ""}`}>
            <span className="bar"></span>
            <span className="bar"></span>
            <span className="bar"></span>
          </div>
        </div>

        <div
          className={`
            navItems
            fixed
            top-0
            left-0
            w-screen
            h-screen
            overflow-hidden
            bg-[#121c29eb]
            flex
            flex-col
            justify-center
            items-center
            transform
            transition-transform
            duration-300
            ease-in-out
            md:static
            md:w-auto
            md:h-auto
            md:flex-row
            md:bg-transparent
            ${isMenuOpen ? "translate-x-0" : "translate-x-full"}
            md:translate-x-0
          `}
        >
          <ul className="nav-ul">
            <li>
              <Link href={`/${currentLocale}/about-us`}>{t("about")}</Link>
            </li>
            {NavItems.map((item, index) => (
              <li key={`${index}-${item}`} className="text-[1rem] font-normal py-4 md:py-0">
                <a
                  href={`#${item.toLowerCase()}`}
                  className={`block text-center cursor-pointer ${
                    activeSection === item.toLowerCase() ? "font-bold" : ""
                  }`}
                  onClick={(e) => handleNavigation(e, item.toLowerCase())}
                >
                  {t(item.toLowerCase())}
                </a>
              </li>
            ))}
            <li>
              <Link href={`/${currentLocale}/faq`}>{t("faq")}</Link>
            </li>
            <li className="p-1">
              <select
                value={currentLocale}
                onChange={handleLanguageChange}
                className="nav-select"
                style={{
                  backgroundImage:
                    "url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='white' viewBox='0 0 24 24'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E\")",
                  backgroundPosition: "right 0.5rem center",
                  backgroundRepeat: "no-repeat",
                  backgroundSize: "1.5em 1.5em",
                  paddingRight: "2rem",
                  direction: currentLocale === "ar" ? "rtl" : "ltr",
                }}
              >
                <option value="en" className="rounded-md">
                  English
                </option>
                <option value="ar">عربي</option>
              </select>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
