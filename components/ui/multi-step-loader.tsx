"use client";

import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";

const CheckIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
    strokeWidth={1.5}
    stroke="currentColor"
    className={cn("w-6 h-6", className)}
  >
    <path d="M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

const CheckFilled = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 24 24"
    fill="currentColor"
    className={cn("w-6 h-6", className)}
  >
    <path
      fillRule="evenodd"
      d="M2.25 12c0-5.385 4.365-9.75 9.75-9.75s9.75 4.365 9.75 9.75-4.365 9.75-9.75 9.75S2.25 17.385 2.25 12Zm13.36-1.814a.75.75 0 1 0-1.22-.872l-3.236 4.53L9.53 12.22a.75.75 0 0 0-1.06 1.06l2.25 2.25a.75.75 0 0 0 1.14-.094l3.75-5.25Z"
      clipRule="evenodd"
    />
  </svg>
);

type LoadingState = {
  text: string;
};

const LoaderCore = ({
  loadingStates,
  value = 0,
  isRTL = false,
}: {
  loadingStates: LoadingState[];
  value?: number;
  isRTL?: boolean;
}) => {
  return (
    <div
      className="flex relative justify-start max-w-xl mx-auto flex-col mt-20 max-h-[300px] px-4"
      dir={isRTL ? "rtl" : "ltr"}
    >
      {loadingStates.map((loadingState, index) => {
        const distance = Math.abs(index - value);
        const opacity = Math.max(1 - distance * 0.2, 0);

        return (
          <motion.div
            key={index}
            dir={isRTL ? "rtl" : "ltr"}
            className="flex items-center mb-4"
            initial={{ opacity: 0, y: -(value * 40) }}
            animate={{ opacity: opacity, y: -(value * 40) }}
            transition={{ duration: 0.5 }}
          >
            {/* Icon with direction-aware spacing */}
            <div className={cn("shrink-0", isRTL ? "ml-3" : "mr-3")}>
              {index > value && <CheckIcon className="text-white" />}
              {index <= value && (
                <CheckFilled
                  className={cn(
                    "text-white",
                    value === index && "text-lime-500 opacity-100"
                  )}
                />
              )}
            </div>

            <span
              className={cn(
                "font-bold text-2xl",
                value === index ? "text-lime-500 opacity-100" : "text-white"
              )}
            >
              {loadingState.text}
            </span>
          </motion.div>



        );
      })}
    </div>
  );
};

export const MultiStepLoader = ({
  loadingStates,
  loading,
  duration = 2000,
  loop = true,
  onClose = () => { },
  bookingLink,
}: {
  loadingStates: LoadingState[];
  loading?: boolean;
  duration?: number;
  loop?: boolean;
  onClose?: () => void;
  bookingLink?: string;
}) => {
  const [currentState, setCurrentState] = useState(0);
  const [confirmed, setConfirmed] = useState(false);
  const t = useTranslations("practices");

  const isRTL = t("dir") === "rtl";

  useEffect(() => {
    if (!loading) {
      setCurrentState(0);
      setConfirmed(false);
      return;
    }

    const timeout = setTimeout(() => {
      setCurrentState((prevState) => {
        if (loop || prevState < loadingStates.length - 1) {
          return (prevState + 1) % loadingStates.length;
        }
        return prevState;
      });
    }, duration);

    return () => clearTimeout(timeout);
  }, [currentState, loading, loop, loadingStates.length, duration]);

  return (
    <AnimatePresence mode="wait">
      {loading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="w-full h-full fixed inset-0 z-[100] flex flex-col items-center justify-center backdrop-blur-2xl"
          dir={isRTL ? "rtl" : "ltr"}
        >
          <button
            onClick={onClose}
            className={cn(
              "absolute p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors z-30",
              isRTL ? "left-6 top-6" : "right-6 top-6" // RTL-aware positioning
            )}
            aria-label={isRTL ? "إغلاق المحمل" : "Close loader"}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={2}
              stroke="currentColor"
              className="w-6 h-6 text-white"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Loader content */}
          <div className="h-96 relative z-10 w-full px-4">
            <LoaderCore
              value={currentState}
              loadingStates={loadingStates}
              isRTL={isRTL}
            />
          </div>

          {/* Confirmation section with RTL support */}
          <div className={cn(
            "mt-[5rem] flex flex-col w-fit max-w-md mx-auto",
            isRTL ? "items-end" : "items-start" // RTL-aware alignment
          )}>
            <div className="z-10 flex items-center text-white">
              <input
                type="checkbox"
                id="confirm"
                checked={confirmed}
                onChange={(e) => setConfirmed(e.target.checked)}
                className={cn(
                  "w-5 h-5 accent-lime-500",
                  isRTL ? "order-2 ml-2" : "order-1 mr-2" // RTL-aware ordering and spacing
                )}
              />
              <label
                htmlFor="confirm"
                className={cn(
                  "text-sm",
                  isRTL ? "order-1 text-right" : "order-2 text-left"
                )}
              >
                {t("description")}
              </label>
            </div>

            {bookingLink && (
              <a
                href={confirmed ? bookingLink : undefined}
                target="_blank"
                rel="noopener noreferrer"
                className={cn(
                  "z-10 mt-6 px-6 py-3 font-semibold rounded-lg transition mx-auto",
                  "text-center w-fit",
                  confirmed
                    ? "bg-white text-black hover:bg-gray-200"
                    : "bg-gray-500 text-white cursor-not-allowed"
                )}
                onClick={(e) => {
                  if (!confirmed) e.preventDefault();
                }}
              >
                {t("next_step")}
              </a>
            )}
          </div>

          {/* Background layer */}
          <div className="bg-gradient-to-t inset-x-0 z-0 bottom-0 bg-[#0b1119] dark:bg-black h-full absolute [mask-image:radial-gradient(900px_at_center,transparent_30%,white)]" />
        </motion.div>
      )}
    </AnimatePresence>
  );
};