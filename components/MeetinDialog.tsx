"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "./ui/button";
import { Checkbox } from "./ui/checkbox";
import { Label } from "./ui/label";
import { useTranslations } from "next-intl";
import { useRouter, usePathname } from "next/navigation";
import { useCookie } from "@/components/context/CookieContext";
import Image from "next/image";

interface MeetinDialogProps {
  id?: string;
  booking: string;
  name: string;
  title: string;
  isRtl: boolean;
  onConfirm?: () => void;
}

const MeetinDialog: React.FC<MeetinDialogProps> = ({
  booking,
  name,
  title,
  isRtl,
  onConfirm,
}) => {
  const { dialogOpened, updateDialogOpened } = useCookie();
  const [open, setOpen] = useState(false);
  const [check, setCheck] = useState(false);
  const [currentLocale, setCurrentLocale] = useState("en");
  const t = useTranslations("practices");
  const router = useRouter();
  const pathname = usePathname();
  const practiceItems: number[] = Array.from({ length: 9 }, (_, i) => i + 1);

  useEffect(() => {
    const locale = window.location.pathname.split("/")[1] || "en";
    setCurrentLocale(locale);
  }, []);

  useEffect(() => {
    setOpen(dialogOpened);
  }, [dialogOpened]);

  const handleConfirm = () => {
    if (check) {
      updateDialogOpened(true);
      onConfirm?.();
    }
  };

  const handleCheckboxChange = () => {
    setCheck((prev) => !prev);
  };

  const handlePracticesClick = async () => {
    setOpen(false);
    handleConfirm();

    const isHomePage = pathname === `/${currentLocale}` || pathname === "/";

    if (!isHomePage) {
      await router.push(`/${currentLocale}`);
      setTimeout(() => {
        const practicesSection = document.getElementById("practices");
        if (practicesSection) {
          practicesSection.scrollIntoView({ behavior: "smooth", block: "start" });
        }
      }, 500);
    } else {
      const practicesSection = document.getElementById("practices");
      if (practicesSection) {
        practicesSection.scrollIntoView({ behavior: "smooth", block: "start" });
      }
    }
  };

  return (
    <>
      {dialogOpened ? (
        <a
          href={booking}
          target="_blank"
          rel="noopener noreferrer"
          className="ml-3 text-main_color font-semibold hover:underline"
          aria-label={`Book a session with ${name}`}
        >
          {title}
        </a>
      ) : (
        <Dialog open={open} onOpenChange={setOpen}  >
          <DialogTrigger onClick={() => setOpen(true)}>
            <span className="text-main_color font-semibold hover:underline">{title}</span>
          </DialogTrigger>

          <DialogContent className="max-h-[90vh] overflow-y-auto max-w-2xl">
            <DialogHeader>
              <div className="py-5">
                <DialogTitle className="text-center font-extrabold text-3xl">
                  {t("title")}
                </DialogTitle>
                <DialogDescription className="pt-4 text-center text-muted-foreground">
                  {t("description")}
                </DialogDescription>
              </div>
            </DialogHeader>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 px-4">
              {practiceItems.map((id) => {
                const imgSrc = t(`items.${id}.img`, { defaultValue: "" });
                const title = t(`items.${id}.title`, { defaultValue: `Practice ${id}` });

                return (
                  <div
                    key={id}
                    className="flex items-start gap-3 bg-muted/30 rounded-xl shadow-sm p-4"
                  >
                    <div className="w-14 h-14 flex-shrink-0">
                      <Image
                        src={imgSrc}
                        alt={`${title} practice icon`}
                        width={50}
                        height={50}
                        className="rounded-full object-contain"
                      />
                    </div>
                    <div className="text-md text-black font-medium text-muted-foreground">
                      {title}
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="flex flex-col items-center gap-4 pt-6">
              <div className="flex items-center space-x-2">
                <Checkbox id="confirm" checked={check} onCheckedChange={handleCheckboxChange} className="w-5 h-5 p-0 mx-2" />
                <Label htmlFor="confirm" className="text-sm text-muted-foreground">
                  {isRtl ? "أؤكد أنني قرأت التعليمات" : "I confirm that I have read the instructions"}
                </Label>
              </div>

              <a href={check ? booking : "#"} target="_blank" rel="noopener noreferrer">
                <Button
                  disabled={!check}
                  onClick={handlePracticesClick}
                  className="bg-main_color px-6 py-2"
                >
                  {t("next_step") || "Next"}
                </Button>
              </a>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

export default MeetinDialog;
