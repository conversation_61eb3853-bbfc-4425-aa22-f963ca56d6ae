{"header": {"about": "About us", "services": "Services", "team": "Team", "join": "Join", "faq": "FAQ", "contact us": "Contact Us"}, "landing": {"title": "Get FREE Expert Guidance to Accelerate Your Tech Career!", "description": "{P8Y} offers free consultation sessions led by experts in software development, supporting individuals at all professional levels (from beginners to experienced developers) in overcoming technical challenges, enhancing their skills, and achieving meaningful growth in their careers.", "button_title": "Explore Guides", "ok": "OK", "cancel": "Cancel", "start_here": "👋 Start your journey here! Click to find your perfect mentor"}, "about": {"about": {"title": "About", "aboutDescription": "At {P8Y}, we are committed to guiding software professionals on their learning journey. Our platform connects learners with specialized experts across various fields, including software engineering, artificial intelligence, mobile applications, and web development. This ensures personalized learning experiences while fostering a community dedicated to knowledge sharing and professional growth in the software industry.", "briefDescription": "{P8Y} connects software professionals with specialized experts for personalized learning experiences and professional growth in the tech industry."}, "learnMore": "Learn More About Us", "technologiesTitle": "Technologies We Cover", "technologiesDescription": "Our experts specialize in a wide range of modern technologies", "Why": {"title": "Why ", "1": {"title": "Community Focus", "content": "Collaborative, supportive, and respectful."}, "2": {"title": "Diverse Expertise", "content": "Guides with varied backgrounds and skills."}, "3": {"title": "Open Learning", "content": "Encouraging dialogue and professionalism."}, "missionTitle": "Our Mission", "missionDescription": "Supporting professional development and growth in the software industry by empowering technology professionals with the knowledge and guidance needed to succeed in the rapidly evolving tech landscape. We strive to build a strong learning community that connects experts with learners, fostering knowledge exchange, enhancing practical skills, and helping individuals achieve their career aspirations."}}, "practices": {"dir": "ltr", "title": "{P8Y} Guidelines for an Ideal Consultation", "description": "Please read the guidelines to ensure a smoother experience", "goPractises": "Take me to the guidelines", "next_step": "Book Now", "items": [{"text": "Use a VPN for secure access."}, {"text": "Select the correct local time with VPN."}, {"text": "Check your email before the session."}, {"text": "Test your internet and devices."}, {"text": "Prepare questions in advance."}, {"text": "Engage actively and take notes."}, {"text": "Respect time and avoid off-topic talk."}, {"text": "Give feedback to improve the experience."}, {"text": "Follow up with the expert post-session."}]}, "team": {"title": "{P8Y} Experts", "items": {"Abdulrahman_Babil": {"name": "<PERSON><PERSON><PERSON>", "linkedin": "https://www.linkedin.com/in/abdbabil/", "booking": "https://tidycal.com/1j5rn83/meet-with-abdul-babil-prohelpify", "company": "Meta", "position": "Software Engineer", "topics": {"Backend Programming": true, "Android": true}, "targetLevels": {"Senior": true, "Senior+": true}, "bio": "Adaptable tech enthusiast thriving in challenging environments. Skilled in Backend development, DevOps, Data Engineering, and various programming languages", "image": "/assists/experts/<PERSON><PERSON>man <PERSON>.webp"}, "Mohamad_Al_Mdfaa": {"name": "Mohamad Al Mdfaa", "linkedin": "https://www.linkedin.com/in/mohamad-al-mdfaa/", "booking": "https://calendly.com/mohamad-almdfaa/free-short-call", "company": "MRob Lab - Skoltech", "position": "PhD candidate & Research Intern", "topics": {"Machine Learning": true, "Deep Learning": true, "Computer Vision": true, "NLP": true, "Building End-to-End ML Systems": true}, "targetLevels": {"Any": true}, "bio": "Ph.D. candidate and seasoned Machine Learning Engineer specializing in 3D Computer Vision and Visual SLAM, with expertise in Python, C++, and cloud-based ML systems", "image": "/assists/experts/<PERSON><PERSON>ad Al Mdfaa.webp"}, "Abdullah_Alsaidi": {"name": "<PERSON>", "linkedin": "https://www.linkedin.com/in/abdu<PERSON>-al<PERSON><PERSON>?latest", "booking": "https://calendly.com/abdullah-alsaidi16/one-on-one-with-me", "company": "<PERSON><PERSON><PERSON>", "position": "Software Engineer", "topics": {"Backend": true, "Related Topics": true}, "targetLevels": {"Undergraduate": true, "Fresh": true, "Junior": true}, "bio": "Versatile Software Engineer with expertise in back-end development, data science, and machine learning. Skilled in deploying pricing models, CI/CD workflows, and analyzing satellite data", "image": "/assists/experts/<PERSON>.webp"}, "Rana_Alhuniess": {"name": "<PERSON>", "linkedin": "https://www.linkedin.com/in/rana-alhuniess/", "booking": "https://calendly.com/rana-alhuniess/book-a-free-call", "company": "BeeOrder", "position": "Senior Full Stack Engineer", "topics": {"Backend": true, "Related Topics": true}, "targetLevels": {"Undergraduate": true, "Fresh": true, "Junior": true}, "bio": "Back-end and Mobile App Developer with expertise in PHP, Laravel, JS, NodeJs, C#, Xamarin, and SQL. Bachelor's in IT & Software Engineering from Damascus University", "image": "/assists/experts/<PERSON>.webp"}, "Obay_Daba": {"name": "<PERSON><PERSON>", "linkedin": "https://www.linkedin.com/in/obay-daba/", "booking": "https://calendly.com/dj-o-byte", "company": "Priceloop AI", "position": "Full stack Engineer | Software Engineer", "topics": {"Software": true, "Development": true}, "targetLevels": {"Undergraduate": true, "Fresh": true, "Junior": true, "Mid": true}, "bio": "Dedicated Software Engineer with a focus on Artificial Intelligence, driven by a passion for building impactful solutions. Educated at Damascus University", "image": "/assists/experts/obay.webp"}, "Shadi_Eid": {"name": "<PERSON><PERSON><PERSON>", "linkedin": "https://www.linkedin.com/in/shadira<PERSON>teid/", "booking": "https://calendly.com/shadi-eid", "company": "<PERSON><PERSON><PERSON><PERSON>", "position": "Frontend Developer | Scrum Master", "topics": {"Frontend": true, "Scrum and Project Management": true}, "targetLevels": {"Undergraduate": true, "Fresh": true, "Junior": true, "Mid": true}, "bio": "Web Developer with 4+ years in front-end development, specializing in Angular and responsive design. Holds a technical diploma in Software Engineering from Damascus University", "image": "/assists/experts/shadi eid.webp"}, "Hamza_Rabah": {"name": "<PERSON><PERSON>", "linkedin": "https://www.linkedin.com/in/hamzarabah/", "booking": "https://calendly.com/hamza-rabah/free-short-call", "company": "Apaleo GmbH", "position": "Solutions Architect", "topics": {"Software Development": true}, "targetLevels": {"Mid": true, "Senior": true, "Senior+": true}, "bio": "Innovative Technologist, expert in developing web and mobile applications. Known for streamlining tech stacks and leading cohesive teams.", "image": "/assists/experts/<PERSON>za <PERSON>.webp"}, "Aghiad_Odeh": {"name": "<PERSON><PERSON><PERSON><PERSON>", "linkedin": "https://www.linkedin.com/in/aghiad-odeh-*********/", "booking": "https://calendly.com/aghiadodeh/consultant-meeting", "company": "<PERSON><PERSON><PERSON><PERSON>", "position": "Mobile Engineer", "topics": {"Architecture": true, "Software Development": true, "Android Native ": true, "Flutter": true}, "targetLevels": {}, "bio": "Multi-platform developer experienced in Android, iOS, and Flutter, with strong skills in Java, Kotlin, SwiftUI, and Dart. Expert in RESTful/GraphQL  APIs", "image": "/assists/experts/Aghiad Odeh.webp"}, "Ahmad_Yones": {"name": "<PERSON>", "linkedin": "https://www.linkedin.com/in/ahmadmhdyones/", "booking": "https://calendly.com/ahmadmhdyones", "company": "<PERSON><PERSON><PERSON><PERSON>", "position": "Full stack Engineer", "topics": {"Frontend": true, "Backend": true, "Vue js": true, "Node js": true, "Computer Science": true, "Competitive Programming": true, "Software Development": true, "Web Development": true, "Self-development/growth": true}, "targetLevels": {"Undergraduate": true, "Fresh": true}, "bio": "Full-stack (MERN) developer with a flair for building complex applications. Skilled in front-end and back-end technologies, REST APIs, and GraphQL", "image": "/assists/experts/<PERSON>.webp"}, "Abd_AlRahman_AlAzhurry": {"name": "<PERSON>", "linkedin": "https://www.linkedin.com/in/abdal<PERSON><PERSON>/", "booking": "https://calendly.com/a_azhurry", "company": "Maliatec - Erbil", "position": "Web Developer/Solution Implementer", "topics": {" Web Development & Programming": true, "Database Management Systems": true, "Application Design & Support": true, "Knowledge Sharing & Accessibility": true, "Translation & Proofreading": true}, "targetLevels": {}, "bio": "I am a professional web developer with extensive experience in programming languages and database management systems. I have successfully designed, developed, and supported live-use applications, including a project in partnership with UNHCR that has been in operation since 2015.", "image": "/assists/experts/<PERSON>.webp"}, "Arwan_Raslan": {"name": "<PERSON><PERSON><PERSON>", "linkedin": "https://www.linkedin.com/in/arwan-raslan-14590a147/", "booking": "https://calendly.com/arwan", "company": "<PERSON><PERSON><PERSON><PERSON>", "position": "DevOps Engineer", "topics": {"DevOps": true, "Linux Systems Administration": true, "Cloud Platforms": true, "Automation Tools": true, "CI/CD": true}, "targetLevels": {"Mid": true, "Senior": true, "Senior+": true}, "bio": "I am a DevOps Engineer with 4 years of experience in developing, deploying, and maintaining software applications in agile environments. With over 10 years in Linux system administration, I excel in managing complex systems, leveraging DevOps tools, cloud platforms, and automation technologies to streamline processes and enhance collaboration.", "image": "/assists/experts/arwan.webp"}, "Mouhand Alkadri": {"name": " <PERSON><PERSON><PERSON>", "linkedin": "https://www.linkedin.com/in/mouhand-alkadri/", "booking": "https://calendly.com/mouhand-alkadri/book-a-call-with-mouhand", "company": "<PERSON><PERSON><PERSON><PERSON>", "position": "Backend Developer", "topics": {"Backend": true, "MLOps": true, "Software Development": true}, "targetLevels": {}, "bio": "Backend engineer. Database enthusiast. With experience in MLOps. Passionate about all tech stuff. Enjoy to write about it in my Medium but I don't always do. Lead multiple teams in the development phase of different projects. Lifelong learner", "image": "/assists/experts/mouhand-alkadri.webp"}, "Ahmad AlShalabi": {"name": "<PERSON>", "linkedin": "https://www.linkedin.com/in/ahmad-al-shalabi/", "booking": "https://calendly.com/ahmad-alshalabi/30min", "company": "Federal Tax Authority (UAE)", "position": "Senior Software Engineer, Mendix Expert", "topics": {"Mendix Application Architecture , Developmennt and best practices": true}, "targetLevels": {}, "bio": "A seasoned software engineer specializing in system development (web and mobile applications) using Mendix Low-Code. Extensive experience in designing and building software solutions that combine high performance, strong security, scalability, and system integration design.", "image": "/assists/experts/<PERSON>-<PERSON>shalabi.webp"}, "Roduan Kareem Aldeen": {"name": "<PERSON><PERSON>", "linkedin": "https://www.linkedin.com/in/RoduanKD/", "booking": "https://calendly.com/roduankd/prohelpify-session", "company": "madewithlove", "position": "Software Engineer", "topics": {"Software Engineering": true, "Web Development": true, "Laravel": true, "VueJS": true, "Testing": true}, "targetLevels": {}, "bio": "As a Software Engineer at madewithlove, I specialize in guiding mid-level developers through hands-on challenges in Web Development, Laravel, Vue.js, and Testing. Whether you're refining full-stack architecture, optimizing code quality, or troubleshooting complex workflows", "image": "/assists/experts/Roduan-kareem.webp"}, "Rashad Kokash": {"name": "<PERSON><PERSON><PERSON>", "linkedin": "https://www.linkedin.com/in/rashad-kokash/", "booking": "https://calendly.com/rashadksh/30min", "company": "Misraj Technology", "position": "Full Stack Web Developer", "topics": {"Node js": true, "React js": true, "Golang": true, "Docker": true, "Linux": true, "Aws": true}, "targetLevels": {}, "bio": "A web developer with over 8 years of experience, specialized in Node.js, React.js, and Golang. Has more than 2 years of experience in DevOps and System Administration.  Holds a Bachelor's degree in Artificial Intelligence from Damascus University.", "image": "/assists/experts/Rashad-Ko<PERSON>.webp"}, "Amer Ghazal": {"name": "<PERSON><PERSON>", "linkedin": "https://www.linkedin.com/in/amer-ghazal", "booking": "https://calendly.com/amer-ghazal/prohelpify-menoting", "company": "Micropolis Robotics", "position": "Senior Robotics Software Engineer ", "topics": {"Robotics": true, "AI": true, "ComputerVision": true, "#SoftwareDevelopment": true}, "targetLevels": {}, "bio": "A seasoned robotics software engineer specializing in autonomous systems and AI-driven robotics. Extensive experience in leading teams to develop and optimize software for autonomous navigation, mission planning, and real-time control using C++, ROS, NVIDIA Omniverse,  and advanced algorithms design. Proven ability to architect, deploy, and manage high-performance, scalable robotics solutions, successfully leading autonomy teams to deliver fully autonomous robots for real-world applications.", "image": "/assists/experts/amer-ghazal.webp"}}, "meetExperts": "Meet the Expert", "showMore": "Show More", "showLess": "Show Less"}, "guide_section": {"section_Id": 1, "section_image": "/assists/avatars/Join-1.svg", "section_title": "Can't find the right guide for your learning needs?", "section_desc": "At {P8Y}, we understand that every learner's journey is unique. If you haven't found a guide that matches your specific requirements, we're here to help! Simply let us know your preferences, and we'll do our best to connect you with a guide who fits your learning goals.", "section_button_title": "Ask for a Guide"}, "joinUs_section": {"section_Id": 2, "section_image": "/assists/avatars/Join-Team.webp", "section_title": "Join Our Team of Expert Guides", "section_desc": "Are you passionate about sharing your knowledge and expertise? Join our community of guides at {P8Y} and inspire the next generation.", "section_sup_title": "Why Become a Guide?", "section_items": {"1": {"title": "Impactful Mentorship:", "desc": "Make a difference in someone's learning journey."}, "2": {"title": "Community Engagement:", "desc": "Connect with a diverse community of learners."}, "3": {"title": "Flexible Schedule:", "desc": "Set your availability and help learners at times that suit you."}}, "section_button_title": "Apply to be a Guide"}, "contactUs": {"contactUs_image": "/assists/avatars/contactUs.svg", "contactUs_title": "Contact Us", "contactUs_quastion": "Have Questions or Suggestions?", "contactUs_desc": "We're always here to help. If you have any questions, suggestions, or need assistance, please reach out to us through our online form.", "contactUs_button_title": "Reach out via Form", "feedback_button": "Share Your Feedback"}, "footer": {"title": "Follow us on"}, "privacy": {"title": "Privacy Policy for Prohelpify", "description": "At Prohelpify, your privacy is a top priority. This Privacy Policy outlines how we handle information when you use our platform. By using Prohelpify, you agree to the practices described in this policy. [Effective Date : 12-2-2025]", "1": {"title": "Limited Information Collection", "description": "", "1": {"title": "No Authentication or Data Storage", "description": "Prohelpify does not require user authentication, and we do not collect or store personal user data. You can book sessions directly through third-party tools like Calendly."}, "2": {"title": "Data via Third-Party Tools", "description": "When you book a session via Calendly, the tool may collect information like your name and email to facilitate scheduling. Please review Calendly's privacy policy for details on how they handle your information."}, "3": {"title": "No Session Recording", "description": "Prohelpify sessions, conducted via Google Meet, are not recorded by us. We also prohibit users and mentors from recording sessions to maintain privacy and trust."}}, "2": {"title": "How We Use Limited Information", "description": "Any personal information shared with Prohelpify (e.g., through voluntary agreements or forms) will only be used to: Facilitate communication between users and mentors. Address specific user queries or requests with explicit consent. We ensure that your data is never used for marketing or other purposes without prior agreement.", "1": {"title": "", "description": ""}, "2": {"title": "", "description": ""}, "3": {"title": "", "description": ""}}, "3": {"title": "Third-Party Services", "description": "We rely on trusted third-party services like Calendly and Google Meet to facilitate bookings and sessions. These tools operate independently, and their privacy policies govern how they manage your information. Prohelpify does not assume responsibility for their practices.", "1": {"title": "", "description": ""}, "2": {"title": "", "description": ""}, "3": {"title": "", "description": ""}}, "4": {"title": "User Consent and Rights", "description": "", "1": {"title": " Consent for Data Use", "description": "We will only use your personal data if you explicitly agree to it (e.g., for feedback or optional communication). Without consent, your data will remain unused by Prohelpify."}, "2": {"title": "Rights", "description": "You have the right to: Decline to share personal data. Request the removal of any data voluntarily shared with us. To exercise these rights, contact <NAME_EMAIL>"}, "3": {"title": "", "description": ""}}, "5": {"title": "Security and Privacy", "description": "", "1": {"title": "Limited Data Handling", "description": "Since we do not store or process user data directly, our privacy risks are minimal. Third-party services are responsible for securing the limited data they collect."}, "2": {"title": "No Session Recording", "description": "We enforce a strict no-recording policy for all Google Meet sessions, ensuring that interactions remain confidential between users and mentors."}, "3": {"title": "", "description": ""}}, "6": {"title": "Updates to This Privacy Policy", "description": "We may update this policy periodically to reflect changes in our practices or third-party integrations. The updated version will be posted on this page with the 'Effective Date' noted above. Please review this policy regularly.", "1": {"title": "", "description": ""}, "2": {"title": "", "description": ""}, "3": {"title": "", "description": ""}}, "7": {"title": "Contact Us", "description": "If you have questions or concerns about this Privacy Policy or how we handle limited user interactions, please contact us at: <EMAIL>", "1": {"title": "", "description": ""}, "2": {"title": "", "description": ""}, "3": {"title": "", "description": ""}}, "footer": "Thank you for trusting <PERSON><PERSON><PERSON><PERSON> with your learning journey. We are committed to maintaining your privacy and fostering a secure environment."}, "terms": {"title": "Terms and Conditions for Prohelpify", "description": "Welcome to Prohelpify! By accessing or using our platform, you agree to comply with and be bound by the following Terms and Conditions. Please read them carefully before using Prohelpify. If you do not agree to these terms, please refrain from using our services.", "1": {"title": "Use of Prohelpify Services", "description": "", "1": {"title": "Purpose", "description": "Prohelpify is a platform designed to connect aspiring software professionals with expert guides across various fields of technology. The platform facilitates learning sessions but does not directly manage or guarantee the outcomes of these interactions."}, "2": {"title": "Eligibility", "description": "Users must be at least 15 years old to use Prohelpify's services. By using the platform, you confirm that you meet the eligibility requirements and have the authority to agree to these terms."}, "3": {"title": "Acceptable Use", "description": "You agree to use Prohelpify only for lawful purposes and in a manner that does not infringe upon the rights of others. Prohibited activities include but are not limited to: Sharing or uploading offensive, discriminatory, or harmful content.Engaging in activities that disrupt or harm the platform's functionality.Misusing or harassing mentors, users, or other platform participants."}, "4": {"title": "", "description": ""}}, "2": {"title": "Booking and Conducting Sessions", "description": "", "1": {"title": "Booking Process", "description": "Prohelpify uses third-party tools like Calendly to facilitate the booking of learning sessions. By booking a session, you agree to adhere to the third-party tool's terms and conditions."}, "2": {"title": "Session Rules", "description": "Sessions are conducted via Google Meet or similar tools. Recording of sessions is strictly prohibited by both mentors and users.Users and mentors are expected to engage professionally and respectfully."}, "3": {"title": "Cancellations and Rescheduling", "description": "Cancellations or rescheduling of sessions should be communicated promptly (before at least 12 hours) through the appropriate channels."}, "4": {"title": "Session Termination", "description": "Prohelpify reserves the right to terminate or cancel a session if the user fails to follow the practices outlined on the platform, such as maintaining professionalism, being punctual, or respecting the mentor's time."}}, "3": {"title": "Responsibilities and Liabilities", "description": "", "1": {"title": "User Responsibilities", "description": "Users are responsible for ensuring they have the required technology and stable internet connection for sessions. Users must prepare for sessions in advance to maximize the learning experience."}, "2": {"title": "Prohelpify's Role", "description": "Prohelpify acts as a facilitator and does not guarantee the accuracy, effectiveness, or outcomes of mentor guidance. We are not responsible for disputes between mentors and users."}, "3": {"title": "Limitation of Liability", "description": "Prohelpify is not liable for any direct, indirect, incidental, or consequential damages arising from the use of the platform or services."}, "4": {"title": "", "description": ""}}, "4": {"title": "Intellectual Property", "description": "All content, trademarks, logos, and other intellectual property on the Prohelpify platform are owned by Prohelpify or its licensors. Unauthorized use, reproduction, or distribution of Prohelpify's content is prohibited.", "1": {"title": "", "description": ""}, "2": {"title": "", "description": ""}, "3": {"title": "", "description": ""}, "4": {"title": "", "description": ""}}, "5": {"title": "Privacy and Data Usage", "description": "Prohelpify's Privacy Policy outlines how user data is handled. By using the platform, you acknowledge and agree to the terms of the Privacy Policy.", "1": {"title": "", "description": ""}, "2": {"title": "", "description": ""}, "3": {"title": "", "description": ""}, "4": {"title": "", "description": ""}}, "6": {"title": "Amendments and Updates", "description": "Prohelpify reserves the right to modify or update these Terms and Conditions at any time. Any changes will be effective immediately upon posting on the platform. Users are encouraged to review these terms regularly.", "1": {"title": "", "description": ""}, "2": {"title": "", "description": ""}, "3": {"title": "", "description": ""}, "4": {"title": "", "description": ""}}, "7": {"title": "Contact Us", "description": "If you have questions or concerns about these Terms and Conditions, please contact us at: <EMAIL>", "1": {"title": "", "description": ""}, "2": {"title": "", "description": ""}, "3": {"title": "", "description": ""}, "4": {"title": "", "description": ""}}, "footer": "Thank you for using Prohelpify. We are committed to fostering a professional and enriching learning environment for all users."}, "faq": {"title": "Prohelpify FAQ's", "button": "Show More", "1": {"q": "What is Prohelpify?", "a": "Prohelpify is a platform that connects aspiring software professionals with expert guides to help them learn and grow in their tech careers."}, "2": {"q": "How does Prohelpify work?", "a": "Prohelpify allows users to book one-on-one learning sessions with expert mentors through tools like Calendly. Sessions are conducted online via Google Meet, focusing on software development, AI, data science, DevOps, and more."}, "3": {"q": " Who are the mentors on Prohelpify?", "a": "Our mentors are experienced professionals in software and technology fields, such as software engineering, web development, machine learning, and DevOps. They guide users based on their expertise and practical knowledge."}, "4": {"q": "Do I need to create an account to use Prohelpify?", "a": "No, Prohelpify doesn't require user authentication. You can book a session directly without creating an account."}, "5": {"q": "How can I book a session?", "a": "You can book a session with a mentor through Calendly links provided on the platform. These links will guide you to select a suitable time for your session."}, "6": {"q": "Are the sessions recorded?", "a": "No, Prohelpify enforces a strict no-recording policy for all sessions. Recording sessions is prohibited for both users and mentors to maintain privacy."}, "7": {"q": "How much does it cost to use Prohelpify?", "a": "ProHelpify is entirely free and all our services aim to help you with your career."}, "8": {"q": "Can I cancel or reschedule a session?", "a": "Yes, you can cancel or reschedule a session through Calendly. Make sure to communicate any changes promptly to respect the mentor's time."}, "9": {"q": "What happens if I don't follow the session rules or practices?", "a": "Prohelpify reserves the right to terminate or cancel your session if you fail to follow the guidelines outlined on the website, such as punctuality, professionalism, or mutual respect."}, "10": {"q": "Does Prohelpify collect or store my personal data?", "a": "No, Prohelpify does not collect or store personal user data. However, third-party tools like Calendly may collect minimal information (e.g., name and email) for scheduling purposes."}, "11": {"q": "How can I become a mentor on Prohelpify?", "a": "To apply as a mentor, fill out the mentor application form on our website. Once submitted, our team will review your application and get back to you."}, "12": {"q": "What topics can I learn about on Prohelpify?", "a": "Prohelpify offers mentorship in areas like: Software engineering. Web and mobile development. Machine learning and AI .Data science . DevOps engineering . Career development in tech"}, "13": {"q": "Can I provide feedback about my session?", "a": "Yes, we encourage users to provide constructive feedback about their sessions. This helps us and our mentors improve the overall learning experience."}, "14": {"q": "What makes Prohelpify different from other platforms?", "a": "Prohelpify focuses on one-on-one mentorship tailored to your specific learning journey. We prioritize professional guidance, active engagement, and collaborative learning to help you succeed in your tech career."}, "15": {"q": "How can I get the most benefit from my Prohelpify session?", "a": "Prepare in Advance: Identify the specific topics or questions you want to address and share them with your guide ahead of time if possible. Engage Actively: Listen attentively, ask thoughtful questions, and take notes during the session. Stay Focused: Minimize distractions to ensure you can fully concentrate on the discussion. Seek Feedback: Ask your guide for feedback on your progress or understanding of the topic. Follow Up: After the session, review your notes and implement the advice given to see the best results."}}, "companies": {"title": "Experience from Top Companies"}, "statistics": {"title": "Our Impact in Numbers", "stats": [{"title": "Expert Mentors", "value": 15, "suffix": "+", "description": "Industry experts from local and global tech giants like Meta, Tradinos, and more."}, {"title": "Sessions Delivered", "value": 35, "suffix": "+", "description": "Personalized mentorship sessions conducted successfully."}, {"title": "Satisfaction Rate", "value": 98, "suffix": "%", "description": "Positive feedback and high ratings from our mentees."}, {"title": "Skills Covered", "value": 30, "suffix": "+", "description": "Covering technical and soft skills like Frontend, Backend, and more."}, {"title": "Active Mentees", "value": 30, "suffix": "+", "description": "Developers who are actively improving their skills with ProHelpify."}]}, "notFound": {"title": "Page Not Found", "description": "This page doesn't Exist try going back to the home page", "button": "Home"}, "tech": {"title": "Topics We Cover"}, "testimonials": {"title": "Success Stories", "subtitle": "Real results from our mentorship sessions", "mentor": "Mentor", "topic": "Topic", "array": [{"name": "<PERSON><PERSON><PERSON>", "mentor": "ٌٌRodun <PERSON><PERSON><PERSON>", "gender": "boy", "content": "The session with the mentor was one of the best experiences I’ve had in my professional journey as a developer. It opened my eyes to important aspects I hadn’t seen before , especially in building a strong personal brand and navigating the workplace smartly. For the first time, I felt like I had a clear roadmap ahead of me, not just technically, but also in terms of professional and personal growth.", "skill": "Personal branding"}, {"name": "<PERSON>", "mentor": "ٌRashad Ko<PERSON>h", "gender": "boy", "content": "<PERSON><PERSON><PERSON> is a wonderful person I previously trained under his supervision ,he has an excellent approach ,I recommend new learners to contact <PERSON><PERSON><PERSON> to benefit from his expertise", "skill": "Interview Preperation"}, {"name": "<PERSON><PERSON><PERSON>", "mentor": "<PERSON><PERSON>", "gender": "boy", "content": "Honestly, it was super helpful! I felt like someone really understood what I needed and helped me get past my challenges fast.", "skill": "Career Consultment"}, {"name": "<PERSON>", "mentor": "<PERSON><PERSON><PERSON><PERSON>", "gender": "girl", "content": "I was lost in my educational path and started to asked Mr <PERSON><PERSON><PERSON><PERSON> about the solutions and what should I do he listened to me and gave me all the time to talk about my problems and started to found the solutions to every point I asked for, And gave me more passion to continue and try more to achieve my roles thanks alot it was the best experience 🙏🏻🙏🏻", "skill": "Educational Path"}, {"name": "<PERSON><PERSON>", "mentor": "<PERSON><PERSON><PERSON>", "gender": "boy", "content": "excelent experince , it enoghtened my next steps", "skill": "Self Improvement"}, {"name": "<PERSON><PERSON>", "mentor": "<PERSON>", "gender": "girl", "content": "<PERSON><PERSON> was good listener and communictor , she helped me analyze the points in my mind and put everything in its right place.It was a really exciting session and we have planned to have another one", "skill": "Career Consultment"}, {"name": "<PERSON><PERSON><PERSON> Naser", "mentor": "<PERSON><PERSON><PERSON>", "gender": "boy", "content": "The session enhanced my expertise in data preprocessing and highlighted the importance of leveraging the efforts of others. I am eager to apply these insights to my project.", "skill": "Data Preprocessing"}, {"name": "<PERSON>", "mentor": "<PERSON>", "gender": "boy", "content": "Meeting with the programming expert was truly amazing in every sense of the word! The session was inspiring and extremely helpful, and I am grateful for the opportunity to learn from him. Thank you very much for the great effort you put into helping me develop my programming skills!", "skill": "Career Consultment"}, {"name": "<PERSON><PERSON><PERSON>", "mentor": "<PERSON><PERSON><PERSON>", "gender": "boy", "content": "Great listener, Great adviser", "skill": "Self Improvement"}, {"name": "<PERSON><PERSON>", "mentor": "<PERSON><PERSON>", "gender": "girl", "content": "The session shifted my perspective and the guidance and encouragement fueled my motivation.", "skill": "Career Consultment"}, {"name": "<PERSON>", "mentor": "<PERSON><PERSON><PERSON><PERSON>", "gender": "boy", "content": "Working was truly enlightening! His expertise in backend development helped me gain a deeper understanding of the subject matter. I highly recommend his services to anyone looking to enhance their backend skills. Thank you for the valuable insights!", "skill": "Presenting Projects"}, {"name": "<PERSON>", "mentor": "<PERSON>", "gender": "boy", "content": "Excellent and funny conversation with mr. <PERSON>,Very straight forward advises.", "skill": "Finding the First Job"}, {"name": "<PERSON><PERSON><PERSON>", "mentor": "<PERSON><PERSON>", "gender": "boy", "content": "It was a positive experience and I enjoyed talking to the host.", "skill": "Soft Skills and Career Improvement"}, {"name": "Baraa Alfarkh", "mentor": "<PERSON><PERSON>", "gender": "boy", "content": "A consultancy in 1 person ", "skill": "Career Consultment"}, {"name": "<PERSON><PERSON>", "mentor": "<PERSON><PERSON>", "gender": "boy", "content": "<PERSON><PERSON> has a great experience in the field, and he's brilliant in making everything simple no matter how complicated it is. When things get messy or you feel you're suffering from code smell <PERSON><PERSON> is the best guide to eliminate the mess in the best possible way! ", "skill": "Design Patterns"}]}, "mentors": {"findMentor": "Find Your Mentor", "search": "Search for mentors, skills, expertise...", "filters": "Filters:", "techSkills": "Tech Skills", "expertise": "Expertise", "services": "Services", "companies": "Companies", "topics": "Topics", "gender": "Gender", "level": "Your Level", "sessionLanguage": "Session Language", "clearAll": "Clear all", "found": "Found", "mentors": "mentors", "sortBy": "Sort by:", "relevance": "Relevance", "experienceHighToLow": "Experience (High to Low)", "experienceLowToHigh": "Experience (Low to High)", "viewProfile": "View Profile", "noMentorsFound": "No mentors found", "tryAdjusting": "Try adjusting your filters or search query", "at": "at", "male": "Male", "female": "Female", "other": "Other", "intern": "Intern", "junior": "Junior", "intermediate": "Intermediate", "senior": "Senior", "expert": "Expert", "previous": "Previous", "next": "Next"}, "wizard": {"title_1": "Find Your", "title_2": "Ideal", "title_3": "Mentor", "question_1": "Ready to find your mentor in TWO clicks ?", "description": "Once you submit your answers, we'll review your needs and email you the recommended mentors list.", "sharing_details": "Sharing detailed information helps us find your perfect mentor!", "start_button": "Start", "next_button": "Next", "back_button": "Back", "submit_button": "Submit", "cancel": "Cancel", "info": {"name": "Name", "email": "Email Address"}, "status": {"question": "What is your current status ?"}, "other_status": {"question": "Please Describe your Current Situation"}, "study": {"question": "What do you want to learn ?"}, "reason": {"question_1": "What is the reason for seeking mentorship?", "question_2": "Please provide more details about your mentorship needs (optional)"}, "cv_linkedin": {"linkedin": "LinkedIn Profile (optional)", "cv": "Upload your CV (optional)", "notes": "Additional Notes (optional)"}, "reasons": {"1": "Interview Preparation", "2": "Career Consultation", "3": "Learning Roadmap", "4": "Other"}, "wizard_dialog": {"button": "Start Now", "title": "Help Finding a Mentor", "description": "Get mentor recommendations within 48 hours via email!", "continue": "Continue", "stay": "No Thanks"}, "success": "We'll Contact You Soon", "success_message": "We have received your request. Our team will get back to you within 24-48 hours with the best mentor match for your needs.", "error": {"submission_failed": "Submission failed. Please try again.", "email_required": "Email is required", "invalid_email": "Please enter a valid email address", "unexpected": "An unexpected error occurred"}, "ok": "OK", "step_1": "Reason", "step_2": "Basic Info", "step_3": "Success"}, "services_section": {"services": [{"title": "Interview Preparation", "description": "Sharpen your interview skills with guidance from experienced professionals. Receive practical tips, mock interview practice, and personalized feedback to boost your confidence and performance."}, {"title": "Career Consultation", "description": "Not sure what’s next in your tech journey? Talk to an expert who understands the industry and can help you identify your strengths, set clear goals, and plan your next steps."}, {"title": "Learning Roadmap", "description": "Build a personalized roadmap based on your background and ambitions. Get structured guidance on what to learn, when, and how . aligned with your career aspirations."}]}}