import { NextResponse } from "next/server";
import { ApiError } from "../utils/ApiError";
import { errorHandler } from "../utils/errorHandler";
import { connectDB } from "../lib/db";
import Mentor from "../../models/Mentor";
import Skill from "../../models/Skill";
import Service from "../../models/Service";
import Topic from "../../models/Topic";
import AreaOfExpertise from "../../models/AreaOfExpertise";
import Company from "../../models/Company";
import { MentorType } from "../utils/types";
import mongoose from "mongoose";

export interface ListResponse<T> {
  data: T[];
  total: number;
  metadata?: any;
}

export interface BaseResponse<T> {
  message?: string;
  success?: boolean;
  data: T;
  statusCode?: number;
}

export async function GET(request: Request) {
  await connectDB();
  try {
    const { searchParams } = new URL(request.url);

    // Check for authorization token
    const authToken = request.headers.get("Authorization") || request.headers.get("authorization");

    let query: any = {};

    if (authToken) {
      // If authorization token is present, return all mentors (MENTOR or ADMIN)
      query = {
        $and: [
          {
            $or: [
              { role: "MENTOR" },
              { role: "ADMIN" }
            ]
          }
        ]
      };
    } else {
      // If no authorization token, only return mentors with profileComplete = true and role = MENTOR
      query = {
        $and: [
          { role: "MENTOR" },
          { profileComplete: true }
        ]
      };
    }
    const populateFields = [
      "current_company",
      "skills",
      "topics",
      // areas_of_expertise is now embedded in the Mentor model
      "services",
    ];

    const searchTerm = searchParams.get("search");
    if (searchTerm) {
      query.$and.push({
        $or: [
          { name_en: { $regex: searchTerm, $options: "i" } },
          { name_ar: { $regex: searchTerm, $options: "i" } },
          { bio_en: { $regex: searchTerm, $options: "i" } },
          { bio_ar: { $regex: searchTerm, $options: "i" } },
        ],
      });
    }

    const gender = searchParams.get("gender");
    if (gender) {
      const validGenders = ["MALE", "FEMALE"];
      const genderUpper = gender.toUpperCase();
      if (!validGenders.includes(genderUpper)) {
        throw new ApiError(400, "Invalid gender parameter");
      }
      query.$and.push({ gender: genderUpper });
    }

    const skill = searchParams.get("skill");
    if (skill) {
      const skillIds = skill.split(",").filter(id => mongoose.Types.ObjectId.isValid(id));
      if (skillIds.length === 0) {
        throw new ApiError(400, "Invalid skill ID format");
      }
      const skillDocs = await Skill.find({ _id: { $in: skillIds } });
      console.log("Skill Documents:", skillDocs);
      if (skillDocs.length === 0) throw new ApiError(404, "No skills found");
      query.$and.push({ skills: { $in: skillDocs.map(doc => doc._id) } });
    }

    const service = searchParams.get("service");
    if (service) {
      const serviceIds = service.split(",").filter(id => mongoose.Types.ObjectId.isValid(id));
      if (serviceIds.length === 0) {
        throw new ApiError(400, "Invalid service ID format");
      }
      const serviceDocs = await Service.find({ _id: { $in: serviceIds } });
      console.log("Service Documents:", serviceDocs);
      if (serviceDocs.length === 0) throw new ApiError(404, "No services found");
      query.$and.push({ services: { $in: serviceDocs.map(doc => doc._id) } });
    }

    const topic = searchParams.get("topic");
    if (topic) {
      const topicIds = topic.split(",").filter(id => mongoose.Types.ObjectId.isValid(id));
      if (topicIds.length === 0) {
        throw new ApiError(400, "Invalid topic ID format");
      }
      const topicDocs = await Topic.find({ _id: { $in: topicIds } });
      console.log("Topic Documents:", topicDocs);
      if (topicDocs.length === 0) throw new ApiError(404, "No topics found");
      query.$and.push({ topics: { $in: topicDocs.map(doc => doc._id) } });
    }

    const expertise = searchParams.get("expertise");
    if (expertise) {
      // Split by comma and filter out empty strings
      const expertiseValues = expertise.split(",").filter(value => value.trim() !== "");
      if (expertiseValues.length === 0) {
        throw new ApiError(400, "Invalid expertise format");
      }

      // Query for areas_of_expertise where title_en is in the expertiseValues
      query.$and.push({
        "areas_of_expertise.title_en": { $in: expertiseValues }
      });
    }

    // Company filter
    const company = searchParams.get("company");
    if (company) {
      const companyIds = company.split(",").filter(id => mongoose.Types.ObjectId.isValid(id));
      if (companyIds.length === 0) {
        throw new ApiError(400, "Invalid company ID format");
      }
      const companyDocs = await Company.find({ _id: { $in: companyIds } });
      console.log("Company Documents:", companyDocs);
      if (companyDocs.length === 0) throw new ApiError(404, "No companies found");
      query.$and.push({ current_company: { $in: companyDocs.map(doc => doc._id) } });
    }

    // English session filter
    const englishOnly = searchParams.get("english_only");
    if (englishOnly === "true") {
      query.$and.push({ english_session: true });
    }

    // Level filter
    const level = searchParams.get("level");
    if (level) {
      const validLevels = ["JUNIOR", "MID", "SENIOR", "EXPERT"];
      const levelUpper = level.toUpperCase();
      if (!validLevels.includes(levelUpper)) {
        throw new ApiError(400, "Invalid level parameter");
      }
      query.$and.push({ level: levelUpper });
    }

    // If no filters are applied, remove $and
    if (query.$and.length === 0) {
      delete query.$and;
    }

    // Pagination
    const page = Math.max(1, parseInt(searchParams.get("page") || "1"));
    const limit = Math.max(1, parseInt(searchParams.get("limit") || "10"));
    const skip = (page - 1) * limit;
    console.log("Pagination Params:", { page, limit, skip });


    // Execute query
    const [mentors, total] = await Promise.all([
      Mentor.find(query)
        .populate(populateFields)
        .skip(skip)
        .limit(limit)
        .select("-password -__v"),
      Mentor.countDocuments(query),
    ]);

    // Log results

    const response: BaseResponse<ListResponse<MentorType>> = {
      success: true,
      message:
        mentors.length === 0
          ? `No mentors found for the specified filters`
          : "Mentors retrieved successfully",
      statusCode: 200,
      data: {
        data: mentors,
        total,
        metadata: {
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      },
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, X-Filter-Complete-Mentors",
      },
    });
  } catch (error) {
    return errorHandler(error);
  }
}

export async function OPTIONS() {
  return new Response(null, {
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, X-Filter-Complete-Mentors",
    },
  });
}