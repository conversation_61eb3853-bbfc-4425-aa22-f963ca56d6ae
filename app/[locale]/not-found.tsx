"use client";

import Image from "next/image";
import React from "react";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { Button } from "@/components/ui/button";
const NotFound = () => {
  const t = useTranslations("notFound");
  return (
    <div className="h-[100%] flex flex-col  justify-center items-center w-full gap-12 py-9">
      <Image
        src={"/assists/icons/not-found.webp"}
        width={350}
        height={512}
        alt="404 Page Not Found illustration"
        className="md:w-[350px] w-[300px]"
        priority={false}
      />
      <div className="flex flex-col items-center md:px-0 px-2 text-center gap-2">
        <h3 className="md:text-[50px] text-[40px] font-bold">{t("title")}</h3>
        <p className="md:text-2xl text-xl  font-normal">{t("description")}</p>
      </div>
        <Link href={"/"} prefetch={true}>
          <Button className="bg-main_color hover:bg-main_color">{t("button")}</Button>
        </Link>
    </div>
  );
};

export default NotFound;
